defmodule Admin.Crm.List do
  @moduledoc """
  This module represents a `List`, that is to say a collection of resources that are considered to be highly related and required to execute a Campaign Setup.

  You may consider this as a "Context" for the Setup Suite. As it provides everything from the original file reference, to the Dial Policy of a given setup. It is everything required that would otherwise require a human to ingest and complete.
  """
  @enforce_keys [:file]

  @name __MODULE__
  alias Admin.Crm.LeadFile.StagedContact
  alias Admin.Crm.{LeadFile, File, Setups}
  alias Admin.Services.S3
  alias Admin.Tools.FileLoader
  alias Explorer.{DataFrame, Series}
  require Ash.Query
  require Explorer.DataFrame

  require Logger

  defstruct name: "",
            # LeadFile
            file: nil,
            ext: "",
            # Explorer.DataFrame
            df: nil,
            uuid: "",
            metrics: [],
            warnings: [],
            rows: 0,
            phone_fields: [],
            type: :standard,
            output: "",
            assignments: %{
              project_id: nil,
              container_id: 0,
              group_id: 0,
              project: nil,
              # FIXME: This shows as default landline, but we want to do a DB call to figure out where the PROJECT_ID is hosted from the parent.
              dialer: :landline
            }

  @phone_columns [:HomePhone, :CompanyPhone, :AltCompanyPhone, :NewPhone]

  @type t :: %__MODULE__{
          name: String.t(),
          file: LeadFile.t() | File.t(),
          ext: String.t(),
          df: DataFrame.t(),
          uuid: String.t(),
          rows: integer(),
          warnings: list(String.t()),
          metrics: list(metrics()),
          type: :standard | :group,
          assignments: assignments()
        }

  @type assignments :: %{
          project_id: integer(),
          container_id: integer(),
          group_id: integer(),
          project: nil | Crm.Project.t(),
          dialer: :landline | :wireless
        }

  @type metrics :: %{
          field: String.t(),
          distinct: integer(),
          blank: integer(),
          ratio: integer(),
          total: integer(),
          # 3 Invalid State Removal
          invalid_states: integer(),
          # 4 Row Removal - null + invalid phones
          invalid_phones: integer(),
          # 6 Load Special State Removals
          load_special_states: integer(),
          # 7 SMS Special State Removals
          sms_special_states: integer()
        }

  @valid_us_states [
    "alabama",
    "alaska",
    "arizona",
    "arkansas",
    "california",
    "colorado",
    "connecticut",
    "delaware",
    "florida",
    "georgia",
    "hawaii",
    "idaho",
    "illinois",
    "indiana",
    "iowa",
    "kansas",
    "kentucky",
    "louisiana",
    "maine",
    "maryland",
    "massachusetts",
    "michigan",
    "minnesota",
    "mississippi",
    "missouri",
    "montana",
    "nebraska",
    "nevada",
    "new hampshire",
    "new jersey",
    "new mexico",
    "new york",
    "north carolina",
    "north dakota",
    "ohio",
    "oklahoma",
    "oregon",
    "pennsylvania",
    "rhode island",
    "south carolina",
    "south dakota",
    "tennessee",
    "texas",
    "utah",
    "vermont",
    "virginia",
    "washington",
    "west virginia",
    "wisconsin",
    "wyoming",
    "district of columbia",
    "american Samoa",
    "guam",
    "northern mariana islands",
    "puerto rico",
    "united states minor outlying islands",
    "virgin islands, u.s."
  ]

  @valid_us_states_abbrev [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
    "DC",
    "AS",
    "GU",
    "MP",
    "PR",
    "UM",
    "VI"
  ]

  def new(file) do
    %@name{file: file}
  end

  def new(file, rows) do
    %@name{
      name: file.name,
      file: file,
      ext: file.file_format,
      df: DataFrame.new(rows),
      uuid: file.id
    }
  end

  def new(name, file, ext, df, uuid) do
    %@name{
      name: name,
      file: file,
      ext: ext,
      df: df,
      uuid: uuid
    }
  end

  def get_df_size(df) do
    df
    |> DataFrame.to_rows()
    |> Series.from_list()
    |> Series.size()
  end

  def replace_mapped_headings(%@name{} = list, notify_fn) do
    notify_fn.("Pre-processing...")

    incoming_leads = get_df_size(list.df)

    notify_fn.("Incoming Leads: #{incoming_leads}")

    Logger.debug("OG dataframe names: #{inspect(DataFrame.names(list.df))}")

    # current column names
    current_names = DataFrame.names(list.df)

    # use save mapping from DB table
    %Admin.Crm.LeadFile{id: id} = list.file
    Logger.debug("list check: #{inspect(id)}")

    # Note: Refactored from the UploadLive module.
    map = Setups.get_saved_mapping(id)

    name_mapping = Map.get(map, id)

    # Map old names to new names - WORKS YAAAYY
    new_names =
      Enum.map(current_names, fn name ->
        name = String.downcase(String.trim(name))
        # Use the new name if it exists in the mapping
        Map.get(name_mapping, name, name)
      end)

    # Rename the columns into new DataFrame
    df_with_new_names = DataFrame.rename(list.df, new_names)

    Logger.debug("new dataframe names: #{inspect(DataFrame.names(df_with_new_names))}")

    list = update(df_with_new_names, list)

    notify_fn.("1. File Headings Replaced")

    list
  end

  def list_state_replacements(%@name{} = list, notify_fn) do
    rows = DataFrame.to_rows(list.df)

    state_map = Enum.zip(@valid_us_states, @valid_us_states_abbrev) |> Enum.into(%{})

    new_rows =
      for row <- rows do
        if row["homestate"] not in @valid_us_states_abbrev do
          state_abbrev = Map.get(state_map, String.downcase(row["homestate"]), row["homestate"])
          Map.replace(row, "homestate", state_abbrev)
        else
          row
        end
      end

    new_df = DataFrame.new(new_rows)

    Logger.debug("homestate OG: #{inspect(DataFrame.pull(list.df, "homestate"))}")
    Logger.debug("homestate new: #{inspect(DataFrame.pull(new_df, "homestate"))}")

    notify_fn.("3. Eligible States Replaced with abbreviations")

    %{list | df: new_df}
  end

  def list_row_removals(%@name{} = list, notify_fn) do
    incoming_leads = get_df_size(list.df)

    list =
      list
      # |> remove_rows_homestate("homestate", incoming_leads, notify_fn)
      |> remove_rows_homephone("homephone", incoming_leads, notify_fn)

    filtered_df = list.df
    Logger.debug("filtered_df: #{inspect(filtered_df)}")

    %{list | df: filtered_df}
  end

  def remove_rows_homestate(list, "homestate", incoming_leads, notify_fn) do
    rows = DataFrame.to_rows(list.df)

    new_rows =
      for row <- rows, row["homestate"] in @valid_us_states_abbrev do
        row
      end

    new_dataframe = DataFrame.new(new_rows)

    Logger.debug("remove_rows_homestate: #{inspect(DataFrame.pull(new_dataframe, "homestate"))}")

    notify_fn.(
      "4. Records with Invalid States Removed: #{incoming_leads - get_df_size(new_dataframe)}"
    )

    list =
      update_phone_metrics(list, "state_removals", incoming_leads - get_df_size(new_dataframe))

    %{list | df: new_dataframe}
  end

  # FIXME: Replace with ExPhoneNumber
  def remove_rows_homephone(list, "homephone", incoming_leads, notify_fn) do
    new_dataframe =
      list.df
      # empty phones
      |> DataFrame.filter(col("homephone") != "")
      # invalid phones (<10)
      |> remove_rows_homephone("homephone")
      # invalid phones (repetitive)
      |> DataFrame.filter(col("homephone") != "1111111111")
      #
      |> DataFrame.filter(col("homephone") != "0000000000")
      #
      |> DataFrame.filter(col("homephone") != "9999999999")

    Logger.debug("remove_rows_homephone: #{inspect(DataFrame.pull(new_dataframe, "homephone"))}")

    notify_fn.(
      "4. Records with Null and Invalid HomePhones Removed: #{incoming_leads - get_df_size(new_dataframe)}"
    )

    list =
      update_phone_metrics(list, "phone_removals", incoming_leads - get_df_size(new_dataframe))

    %{list | df: new_dataframe}
  end

  def remove_rows_homephone(dataframe, "homephone") do
    rows = DataFrame.to_rows(dataframe)

    new_rows =
      for row <- rows, String.length(row["homephone"]) >= 10 do
        row
      end

    new_dataframe = DataFrame.new(new_rows)

    Logger.debug(
      "remove_rows_homephone_invalid: #{inspect(DataFrame.pull(new_dataframe, "homephone"))}"
    )

    new_dataframe
  end

  #
  def convert_date_fields(%@name{} = list, notify_fn) do
    df_names = DataFrame.names(list.df)
    date_fields = ["expire_date", "qual_date"]

    # Check which date fields exist in the dataframe
    existing_date_fields = Enum.filter(date_fields, &(&1 in df_names))

    if existing_date_fields == [] do
      # No date fields to convert
      list
    else
      new_df =
        existing_date_fields
        |> Enum.reduce(list.df, fn field, acc_df ->
          convert_date_column(acc_df, field)
        end)

      notify_fn.("2. Date fields converted: #{Enum.join(existing_date_fields, ", ")}")
      %{list | df: new_df}
    end
  end

  defp convert_date_column(df, column_name) do
    column_series = DataFrame.pull(df, column_name)

    converted_values =
      column_series
      |> Series.to_list()
      |> Enum.map(&convert_date_value/1)

    # New series with converted dates
    new_series = Series.from_list(converted_values, dtype: :string)

    # Replace the column in the dataframe
    DataFrame.put(df, column_name, new_series)
  end

  defp convert_date_value(value) when is_nil(value) or value == "", do: nil

  defp convert_date_value(value) when is_binary(value) do
    IO.inspect(value, label: "convert_date_value")
    cond do
      # Already in YYYY-MM-DD format
      String.match?(value, ~r/^\d{4}-\d{2}-\d{2}$/) ->
        value

      # Excel serial date (numeric string)
      String.match?(value, ~r/^\d+$/) ->
        case Integer.parse(value) do
          {serial_date, ""} ->
            case excel_serial_to_date(serial_date) do
              {:ok, date} ->
                IO.inspect(date, label: "converted_date_value")
                Date.to_iso8601(date)
              {:error, _} -> value  # Keep original if conversion fails
            end
          _ -> value
        end

      # Keep other formats as-is
      true -> value
    end
  end

  defp convert_date_value(value), do: to_string(value)

  def clean_homepostcode(%@name{} = list, notify_fn) do
    rows = DataFrame.to_rows(list.df)

    new_rows =
      for row <- rows do
        # Logger.debug("homepostcode: #{inspect(row["homepostcode"])}")

        case row["homepostcode"] do
          nil ->
            row

          _ ->
            zip = row["homepostcode"]

            cleaned_value = String.trim(zip) |> String.replace(~r/[\s-]/, "")

            final_zipcode =
              if !String.match?(String.slice(cleaned_value, 0, 3), ~r/^\d+$/) do
                cleaned_value
              else
                if String.length(cleaned_value) < 6 do
                  cleaned_value
                  |> String.pad_leading(5, "0")
                  |> String.slice(-5, 5)
                else
                  left_part =
                    cleaned_value
                    |> String.pad_leading(9, "0")
                    |> String.slice(-9, 9)
                    |> String.slice(0, 5)

                  right_part =
                    cleaned_value
                    |> String.pad_leading(9, "0")
                    |> String.slice(-4, 4)

                  "#{left_part}-#{right_part}"
                end
              end

            # Logger.debug("homepostcode old: #{zip} new: #{final_zipcode}")

            # row_n =
            Map.replace(row, "homepostcode", final_zipcode)

            # Logger.debug("row old: #{inspect(row)} new: #{inspect(row_n)}")
        end
      end

    new_df = DataFrame.new(new_rows)

    notify_fn.("5. Zipcodes Cleaned/Padded")

    %{list | df: new_df}
  end

  def special_state_removals(%@name{} = list, notify_fn) do
    # TODO: Add ContactEngine state removal checks.

    load_state_removals =
      list.file.loads
      |> Enum.flat_map(fn load_record ->
        Map.get(load_record, :load_state_removals, [])
      end)

    Logger.debug("list in pre_process: #{inspect(load_state_removals)}")

    rows = DataFrame.to_rows(list.df)

    # MARK: State filter
    new_rows =
      for row <- rows, row["homestate"] not in load_state_removals do
        row
      end

    new_dataframe = DataFrame.new(new_rows)

    Logger.debug(
      "remove_rows_special state: #{inspect(DataFrame.pull(new_dataframe, "homestate"))}"
    )

    notify_fn.(
      "6. #{inspect(load_state_removals)} Records Removed: #{get_df_size(list.df) - get_df_size(new_dataframe)}"
    )

    list =
      update_phone_metrics(
        list,
        "special_load_state_removals",
        get_df_size(list.df) - get_df_size(new_dataframe)
      )

    %{list | df: new_dataframe}
  end

  def from_aws(path) do
    {:ok, txt} = S3.download_file(path)
    from_text(txt)
  end

  @doc """
  Replaces a lists df with a new one

  Used to apply transformations to a list

  Returns `List` `list` with new `df`

  ## Examples

      iex> list = Admin.Crm.List.new("test.csv")
      iex> new_df = Explorer.DataFrame.new([1,2,3])
      iex> Admin.Crm.List.update(new_df, list)
      %Admin.Crm.List{
        file: "test.csv",
        df: %Explorer.DataFrame{
          columns: ["New1"],
          data: [1, 2, 3],
          index: nil,
          name: nil,
          types: %{"New1" => :integer}
        }
      }
  """
  def update(df, list) do
    %@name{
      list
      | df: df
    }
  end

  def destage_contacts(%@name{file: %LeadFile{id: lead_file_id}}) do
    # BUG This can take a very long time. And timeouts seem to be ignored here.
    # Manual sql query?
    StagedContact
    |> Ash.Query.filter(lead_file_id == ^lead_file_id)
    |> Ash.bulk_destroy(:destroy, %{})

    :ok
  end

  def destage_contacts2(lead_file_id) do
    StagedContact
    |> Ash.Query.filter(lead_file_id == ^lead_file_id)
    |> Ash.bulk_destroy(:destroy, %{}, timeout: :infinity)

    :ok
  end

  # Helper function to parse date fields that might be in different formats
  defp parse_date_field(value) when is_nil(value) or value == "", do: nil

  defp parse_date_field(value) when is_binary(value) do

    IO.inspect(value, label: "parse_date_field")
    cond do
      # ISO8601 format (YYYY-MM-DD)
      String.match?(value, ~r/^\d{4}-\d{2}-\d{2}$/) ->
        {:ok, date} = Date.from_iso8601(value)
        date

      # Parse as Excel serial date (numeric string)
      String.match?(value, ~r/^\d+$/) ->
        case Integer.parse(value) do
          {serial_date, ""} ->
            {:ok, date} = excel_serial_to_date(serial_date)
            date
          _ -> {:error, :invalid_format}
        end

      true ->
        {:error, :invalid_format}
    end
  end

  defp parse_date_field(_), do: {:error, :invalid_format}

  # Convert Excel serial date to Elixir Date
  defp excel_serial_to_date(serial_date) when is_integer(serial_date) and serial_date > 0 do
    base_date = ~D[1899-12-30]

    try do
      result_date = Date.add(base_date, serial_date)
      {:ok, result_date}
    rescue
      _ -> {:error, :invalid_date}
    end
  end

  defp excel_serial_to_date(_), do: {:error, :invalid_date}

  def stage_lead_file(%@name{file: %LeadFile{id: lead_file_id} = lead_file} = list) do
    output =
      list.df
      |> DataFrame.to_rows()
      |> Enum.map(fn map ->
        map
        |> Enum.map(fn {key, value} ->
          case key do
            "expire_date" -> {key, parse_date_field(value)}
            "qual_date" -> {key, parse_date_field(value)}
            _ -> {String.downcase(key), value}
          end
        end
        )
        |> Enum.into(%{})
        |> Map.put("lead_file_id", lead_file_id)
      end)
      |> Ash.bulk_create(StagedContact, :create, return_errors?: true)

    case output do
      %Ash.BulkResult{status: :success} ->
        :ok

      %Ash.BulkResult{status: other, errors: errors, error_count: err_cnt, notifications: notifs} ->
        {:error,
         "Error staging contacts (#{inspect(other)}): Error Count: #{inspect(err_cnt)} Errors: #{inspect(errors)} Notifications: #{inspect(notifs)}"}
    end
  end

  def load(%@name{} = list) do
    Path.extname(list.file)
    |> load(list.file)
  end

  # TODO: Add support for xlsx from the FileLoader module.
  def load(".txt", file), do: load(".csv", file)

  def load(".csv" = ext, file) do
    name = Path.basename(file)
    uuid = Ecto.UUID.generate()

    {:ok, contents} = FileLoader.parse_csv_file(file)
    df = DataFrame.new(contents)

    new(name, file, ext, df, uuid)
  rescue
    e ->
      {:current_stacktrace, stacktrace} = Process.info(self(), :current_stacktrace)

      Appsignal.send_error(
        RuntimeError.exception("Error loading"),
        stacktrace,
        fn span ->
          Appsignal.Span.set_attribute(span, "error", inspect(e))
          Appsignal.Span.set_attribute(span, "file", inspect(file))
        end
      )

      raise "Error loading #{file}: #{e.message}"
  end

  @doc """
  Takes a given (loaded) list and appends the records to the
  dial table.
  """
  def append_dial(%@name{} = list) do
  end

  def from_text(text) do
    pathname = Path.join([System.tmp_dir!(), "paste.csv"])
    Elixir.File.write!(pathname, text)

    list =
      new(pathname)
      |> load()

    {:ok, list}
  end

  @valid_fields ~w(contactid dialid parentprojid projectid importdate exportdate lastupdatedate accountno fname minitial lname fullname title homeadd1 homeadd2 homeapt homecity homestate homepostcode homecountry homephone pobox fax email companyname companyadd1 companyadd2 companycity companystate companypostcode companyphone deleted newphone receivername authfname authlname authtitle authquest question1 question2 question3 question4 question5 question6 spoketo question4a question4b newfirst newlast question13a question13b emailannounce emailoffers nameref1 nameref2 emailref1 emailref2 altcompanyphone numberofmembers misc1 misc2 misc3 misc4 misc5 misc6 misc7 misc8 misc9 misc10 misc11 misc12 misc13 misc14 misc15 misc16 new17 new18 new19 new20 new21 new22 new23 new24 new25 new26 new27 new28 new29 new30 new31 new32 new33 new34 new35 new36 new37 new38 new39 new40 new41 new42 new43 new44 new45 new46 new47 new48 new49 new50 new51 new52 new53 new54 new55 new56 new57 new58 new59 new60 new61 new62 new63 new64 new65 new66 new67 new68 new69 new70 new71 new72 new73 new74 new75 new76 new77 new78 new79 new80 new81 new82 new83 new84 new85 new86 new87 new88 new89 new90 new91 new92 new93 new94 new95 new96 new97 new98 new99 new100 new101 new102 new103 new104 new105 new106 new107 new108 new109 new110 new111 new112 new113 new114 new115 new116 new117 new118 new119 new120 new121 new122 new123 new124 new125 new126 new127 new128 new129 new130 new131 new132 delayship notes sourcecode location pull pullreason qcname qcdate accountnumber2 date_sent sourceid inserted_at)
  @required_fields ~w(sourcecode homephone homestate)
  @doc """
  Checks all field names and returns a list of invalid fields
  """
  def validate_fields(%@name{} = list) do
    fields = DataFrame.names(list.df) |> Enum.map(&String.downcase/1)
    invalid_fields = fields -- @valid_fields
    missing_fields = @required_fields -- fields

    list = %{
      list
      | warnings: Enum.concat(list.warnings, _get_warnings(invalid_fields, missing_fields))
    }

    cond do
      list.warnings != [] ->
        {:error, list}

      true ->
        {:ok, list}
    end
  end

  def validate_field_lengths(%@name{} = list) do
    fields =
      DataFrame.names(list.df)
      |> Enum.map(
        &{
          &1,
          String.downcase(&1) |> String.to_atom()
        }
      )

    rows = DataFrame.to_rows(list.df)

    warnings =
      rows
      |> Enum.with_index()
      |> Enum.reduce([], fn {row, index}, warnings ->
        warnings ++
          (fields
           |> Enum.reduce([], fn {key, name}, warnings ->
             value =
               case Map.get(row, key) do
                 nil -> ""
                 v -> v
               end

             case Crm.Contact.validate_field_length(name, value) do
               :ok ->
                 warnings

               {:error, e} ->
                 warnings ++
                   [
                     "Row #{index + 2} Column #{inspect(key)} Length: #{inspect(byte_size(value))} Error: #{inspect(e)}. Data will be trimmed to fit.\n"
                   ]
             end
           end))
      end)

    list = %{list | warnings: list.warnings ++ warnings}

    cond do
      list.warnings != [] ->
        {:error, list}

      true ->
        {:ok, list}
    end
  end

  def trim_to_fit_field_lengths(%@name{} = list) do
    fields =
      DataFrame.names(list.df)
      |> Enum.map(
        &{
          &1,
          String.downcase(&1) |> String.to_atom()
        }
      )

    map = DataFrame.to_series(list.df)

    df =
      fields
      |> Enum.reduce(map, &_trim_series/2)
      |> DataFrame.new()

    list = %{list | df: df}

    {:ok, list}
  end

  defp _trim_series({key, name}, map) do
    length = Crm.Contact.FieldLengths.field_length(name)

    map
    |> __maybe_trim(length, key)
  end

  defp __maybe_trim(map, _length = nil, _key), do: map

  defp __maybe_trim(map, length, key) do
    series =
      map[key]
      |> Series.transform(&String.slice(&1, 0..(length - 1)))

    %{
      map
      | key => series
    }
  end

  defp _get_warnings([] = _invalid, [] = _missing), do: []
  defp _get_warnings([] = _invalid, missing), do: ["Missing fields: #{Enum.join(missing)}"]
  defp _get_warnings(invalid, [] = _missing), do: ["Invalid fields: #{Enum.join(invalid)}"]

  defp _get_warnings(invalid, missing) do
    ["Invalid fields: #{Enum.join(invalid)}", "Missing fields: #{Enum.join(missing)}"]
  end

  def to_group_calling(%@name{} = list) do
    %{list | type: :group}
  end

  def set_dialer(%@name{} = list, dialer) when is_atom(dialer) do
    %{list | assignments: %{list.assignments | dialer: dialer}}
  end

  def set_project(%@name{} = list, project_id, container_id \\ 0, group_id \\ 0) do
    %{
      list
      | assignments: %{
          list.assignments
          | project_id: project_id,
            container_id: container_id,
            group_id: group_id
        }
    }
  end

  @doc """
  Converts a Lists df to an array of maps for loading into the database

  > #### Warning {: .warning}
  >
  > This may be an expensive operation because `polars` stores data in columnar format.
  """
  def to_rows(%@name{} = list) do
    list.df
    |> DataFrame.to_rows(atom_keys: true)
  end

  ### METRICS
  @deprecated "Use Admin.Crm.ContactEngine.Helpers.phone_fields/1 instead"
  def identify_phone_fields(%@name{} = list) do
    names = DataFrame.names(list.df)

    phone_columns =
      names
      |> Enum.filter(
        &(&1
          |> String.downcase()
          |> String.contains?("phone") && not (&1 |> String.contains?("metadata")))
      )

    %@name{list | phone_fields: phone_columns}
  end

  @doc """
  To correctly gather metrics, we identify and clean the phone fields first.
  """
  def gather_metrics(%@name{} = list, notify_fn) do
    list =
      list
      |> identify_phone_fields()
      |> Admin.Crm.Setups.Helpers.clean_phone_fields()
      |> gather_shape_metrics()
      |> gather_phone_number_metrics()

    notify_fn.("7. All Phones Cleaned")

    list
  end

  defp push_metrics(list, metrics) do
    %@name{list | metrics: Keyword.merge(list.metrics, metrics)}
  end

  defp get_metric(list, metric, default) do
    case Keyword.fetch(list.metrics, metric) do
      {:ok, value} -> value
      :error -> default
    end
  end

  defp gather_shape_metrics(%@name{} = list) do
    {count, _} = DataFrame.shape(list.df)
    list = %{list | rows: count}
    push_metrics(list, count: count)
  end

  def gather_phone_number_metrics(%@name{} = list) do
    names = DataFrame.names(list.df)

    phone_columns =
      if list.phone_fields == [] do
        Logger.debug("Using static phones.")
        @phone_columns
      else
        Logger.debug("Using dynamic phones: #{inspect(list.phone_fields)}")
        list.phone_fields
      end

    phone_columns
    |> Enum.reduce(list, fn column, list ->
      case Enum.member?(names, to_string(column)) do
        true ->
          gather_phone_number_metrics(list, column)

        false ->
          list
      end
    end)
  end

  def gather_phone_number_metrics(%@name{} = list, field) do
    phone_metrics = get_metric(list, :phone_metrics, [])

    phones = list.df[field]
    distinct = Series.n_distinct(phones)
    blank = Series.mask(phones, phones |> Series.equal("")) |> Series.size()
    total = Series.size(phones)

    metric = %{
      field: field,
      distinct: distinct,
      blank: blank,
      ratio: div(total - blank, distinct),
      total: total
    }

    push_metrics(list,
      phone_metrics: [metric | phone_metrics]
    )
  end

  # TODO: Add ContactEngine into metrics.
  def update_phone_metrics(list, type, count) do
    phone_metrics = get_metric(list, :phone_metrics, [])

    metric =
      case type do
        "state_removals" ->
          %{invalid_states: count}

        "phone_removals" ->
          %{invalid_phones: count}

        "special_load_state_removals" ->
          %{special_load_states: count}

        "special_sms_state_removals" ->
          %{special_sms_states: count}
      end

    push_metrics(list,
      phone_metrics: [metric | phone_metrics]
    )
  end

  defimpl String.Chars do
    def to_string(%{df: nil} = list), do: "#List<#{list.name}(unloaded)>"

    def to_string(list) do
      {rows, cols} = DataFrame.shape(list.df)
      "#List<#{list.name}(#{rows},#{cols})>"
    end
  end

  defimpl Inspect do
    import Inspect.Algebra

    def inspect(list, opts) do
      {rows, columns} =
        if list.df do
          Explorer.DataFrame.shape(list.df)
        else
          {0, 0}
        end

      metrics_pretty = to_doc(list.metrics, opts)

      concat([
        line(),
        color("#Admin.Crm.List<", :map, opts),
        nest(
          concat([
            line(),
            "name: ",
            to_doc(list.name, opts),
            ", ",
            line(),
            "file: ",
            to_doc(list.file, opts),
            line(),
            "type: ",
            to_doc(list.type, opts),
            ", ",
            line(),
            "rows: ",
            to_doc(rows, opts),
            ", ",
            line(),
            "columns: ",
            to_doc(columns, opts),
            ", ",
            line(),
            "phone_fields: ",
            to_doc(list.phone_fields, opts),
            ", ",
            line(),
            "metrics: ",
            metrics_pretty
          ]),
          2
        ),
        color(">", :map, opts)
      ])
    end
  end
end
