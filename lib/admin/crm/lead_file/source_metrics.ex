defmodule Admin.Crm.LeadFile.SourceMetrics do
  @moduledoc """
  This `Ash.Resource` contains the information presented to IT Staff that perform Setups, as well as the Account Managers that interact with clients.

  It calculates the much needed metrics for a given Lead File
  """
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "lead_file_source_metrics"
    repo Admin.AdminRepo
  end

  identities do
    # identity of [effort_id, parent_id, source_code]
    # should prevent duplicated numbers and ensure quick lookup

    identity :lead_file_source, [:lead_file_id, :source]
  end

  attributes do
    uuid_primary_key :id

    attribute :source, :string do
      public? true
    end

    attribute :og_leads, :integer do
      public? true
    end

    attribute :unique_leads, :integer do
      public? true
    end

    attribute :duplicated_leads, :integer do
      public? true
    end

    # x
    attribute :records_scrubbed, :integer do
      public? true
    end

    attribute :clean, :integer do
      public? true
    end

    attribute :invalid, :integer do
      public? true
    end

    attribute :blocked, :integer do
      public? true
    end

    attribute :ebr, :integer do
      public? true
    end

    attribute :wireless, :integer do
      public? true
    end

    attribute :viop, :integer do
      public? true
    end

    attribute :dnc, :integer do
      public? true
    end

    attribute :malformed, :integer do
      public? true
    end

    attribute :w_uncallable_state, :integer do
      public? true
    end

    attribute :internal_dnc, :integer do
      public? true
    end

    attribute :ebr_w_uncallable_state, :integer do
      public? true
    end

    attribute :ebr_w_manual, :integer do
      public? true
    end

    attribute :ebr_w_dnc, :integer do
      public? true
    end

    attribute :override_EBR, :integer do
      public? true
    end

    attribute :expired_EBR, :integer do
      public? true
    end

    attribute :code_V_EBR, :integer do
      public? true
    end

    attribute :industry_exception, :integer do
      public? true
    end

    # "had x lost y for remaining z"
    # y
    attribute :lost_records, :integer do
      public? true
    end

    # Callable Names - z (og_leads?)
    # z (og_leads?)
    attribute :net_callable_names, :integer do
      public? true
    end

    # duplicated_leads?
    attribute :net_callable_groups, :integer do
      public? true
    end

    attribute :rnd, :integer do
      public? true
    end
  end

  relationships do
    belongs_to :lead_file, Admin.Crm.LeadFile do
      public? true
      attribute_type :uuid
      attribute_writable? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    create :upsert do
      accept :*
      upsert? true
      upsert_identity :lead_file_source
    end
  end
end
