<.setup_banner setup={@setup} />
<.panel>
  <:title>Lead File Upload</:title>
  <:sub_title>
    Upload your file(s) here. They uploaded to S3 for retention and you will be taken to the next step.
  </:sub_title>
  <:sub_title>
    Currently, this tool accepts XLSX, XLS, and CSV files
  </:sub_title>
  <:sub_title>
    <.process_overview setup={@setup} stage={:upload} />
  </:sub_title>
  <:sub_title>
    <.lead_file_state_list files={@setup.lead_files} states={@states} enable_delete? />
  </:sub_title>
  <:body>
    <div class="flex justify-between">
      <label class="block text-md font-medium text-gray-600 dark:text-brand-50">Lead Files</label>
      <.link
        :if={@setup.lead_files != [] and @all_saved? == true}
        phx-click={JS.patch(~p"/crm/setups/#{@setup}/plan")}
        class="flex text-gray-600 bg-gray-200 bg-opacity-85 hover:border-2 hover:border-gray-300 p-1 rounded-md text-xs"
      >
        Continue <Heroicons.arrow_right class="pl-1 h-4 w-4" />
      </.link>
    </div>
    <.form multipart={true} id="lead_files_form" phx-submit="upload" phx-change="validate">
      <div
        phx-drop-target={@uploads.lead_files.ref}
        class="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6"
      >
        <div class="space-y-1 text-center">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6A2.25 2.25 0 016 3.75h3.879a1.5 1.5 0 011.06.44l2.122 2.12a1.5 1.5 0 001.06.44H18A2.25 2.25 0 0120.25 9v.776"
            />
          </svg>

          <div class="grid text-sm text-gray-600">
            <label class="cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:text-indigo-500 dark:bg-gray-700 dark:text-brand-100">
              <.live_file_input upload={@uploads.lead_files} />
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <small class="text-xs text-gray-500">
            Up to 10 files ready to submit, up to 250MB each
          </small>
        </div>
      </div>
      <div :if={@uploads.lead_files.entries != []}>
        Uploading files:
        <ul>
          <%= for entry <- @uploads.lead_files.entries do %>
            <li>
              <span>{entry.client_name}</span>
              <progress value={entry.progress} max="100">{entry.progress}%</progress>
              <button
                type="button"
                phx-click="cancel-upload"
                phx-value-ref={entry.ref}
                aria-label="cancel"
              >
                &times;
              </button>
            </li>
          <% end %>
        </ul>
        <button class="p-3 rounded-md px-5 text-white bg-indigo-600" type="submit">
          Upload Files
        </button>
      </div>

      <%= for err <- upload_errors(@uploads.lead_files) do %>
        <p class="alert alert-danger">{error_to_string(err)}</p>
      <% end %>
    </.form>

    <label class="block mb-2 text-md font-medium text-gray-600 dark:text-brand-50">
      Heading Mapping
    </label>
    <div :if={@setup.do_rnd?} class="flex space-x-1 text-blue-500 border-blue-400 border-dotted border-1 bg-blue-200 py-1.5 px-1 rounded-md">
      <Heroicons.information_circle class="pl-1 h-6 w-6" />
      <p class="text-xs font-semibold"> RND Required Mapped Fields:  <br> </p>
      <p class="text-xs">  1. Qualification Date to 'qual_date' <b>OR</b> <br> 2. Both Expiration Date + Membership Years to 'expire_date' and 'term' respectively.
     </p>
    </div>
    
<!-- Allow Mapping Save -->
    <div class="w-full">
      <div :if={@default_mappings_loaded.loading} class="flex flex-col items-center opacity-75">
        <p class="text-lg text-gray-700">Loading column data</p>
        <.spinner type="bars" class="pt-3" color="primary" />
      </div>
      <div :if={@default_mappings_loaded.failed} class="flex flex-col items-center">
        <p class="text-lg text-gray-700">Failed to load column data</p>
      </div>
    </div>

    <%= for map <- @default_mappings, {leadfile_id, warning} <- @warnings do %>
      <form phx-change="validate" phx-submit="save_mapping">
        <div
          :if={@default_mappings != %{} and leadfile_id == map["lf_id"]}
          class="h-96 w-auto overflow-y-auto rounded-lg border-t-2 border-gray-300 bg-slate-50 dark:bg-brand-500/20 border-gray-800"
        >
          <div class="sticky top-0 bg-white dark:bg-gray-700 ">
            <p class="text-sm text-gray-500 dark:text-brand-50">
              {fetch_from_lf_name(map["lf_id"])}
            </p>
            <input type="hidden" name="lf_id" value={map["lf_id"]} />
            <div
              :if={@default_mappings != %{}}
              class="flex grid grid-cols-7 gap-2 text-sm font-sans text-slate-400 font-semibold"
            >
              <p class=" col-span-2 pl-3 align-text-bottom">INCOMING</p>
              <p class="col-span-2 col-start-4 pl-3">STAGED FIELD</p>
              <div class="col-span-2 col-start-7">
                <button type="submit" title="Save Mapping" class=" text-xs rounded-md p-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="w-6 h-6 fill-blue-400 hover:fill-blue-600"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M9.75 6.75h-3a3 3 0 0 0-3 3v7.5a3 3 0 0 0 3 3h7.5a3 3 0 0 0 3-3v-7.5a3 3 0 0 0-3-3h-3V1.5a.75.75 0 0 0-1.5 0v5.25Zm0 0h1.5v5.69l1.72-1.72a.75.75 0 1 1 1.06 1.06l-3 3a.75.75 0 0 1-1.06 0l-3-3a.75.75 0 1 1 1.06-1.06l1.72 1.72V6.75Z"
                      clip-rule="evenodd"
                    />
                    <path d="M7.151 21.75a2.999 2.999 0 0 0 2.599 1.5h7.5a3 3 0 0 0 3-3v-7.5c0-1.11-.603-2.08-1.5-2.599v7.099a4.5 4.5 0 0 1-4.5 4.5H7.151Z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <%= for {incoming, mapped_to} <- map , col_warning <- warning do %>
            <%= if col_warning[:heading_incoming]  == incoming do %>
              <div class="flex grid grid-cols-7 gap-2 px-3">
                <div class="mt-2 p-1.5 text-gray-900 text-sm col-span-2">
                  <p name="incoming">{incoming}</p>
                  <input type="hidden" name="incoming_values[]" value={incoming} />
                </div>
                <div class="w-5 h-5 col-span-1 col-start-3 mt-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="size-6 fill-gray-500"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <div class=" col-start-4 col-span-2">
                  <div class="mt-2 text-sm p-1.5 rounded-md ">
                    <select
                      id="staged_field"
                      name="mapping_pairs[]"
                      autocomplete="staged_field-name"
                      class="w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 "
                    >
                      <option>{mapped_to}</option>
                      <option :for={heading <- @staging_headings}>{heading}</option>
                    </select>
                  </div>
                </div>
                <div class="col-start-6 col-span-2 pt-3">
                  <div
                    :if={col_warning[:warning_rows] != 0}
                    class="inline-flex items-center rounded-md bg-red-400/10 px-2 py-1 text-xs font-medium text-red-400 ring-1 ring-inset ring-red-400/20"
                  >
                    <p>{col_warning[:warnings]}</p>
                    <p class="p-1 ring-1 ring-inset ring-red-400/20">
                      {col_warning[:warning_rows]}
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
        <div
          :if={@saving_error != :ok and leadfile_id == map["lf_id"] and @saving == map["lf_id"]}
          class="flex space-x-1 text-red-500 border-red-400 bg-red-200 py-1 px-2 rounded-md"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
            />
          </svg>
          <p class=" font-normal text-xs">{@saving_error}</p>
        </div>
      </form>
    <% end %>
    
<!-- Edit after saving -->
    <%= if @edit != "" do %>
      <form phx-change="validate" phx-submit="save_mapping">
        <%= for {id, mapping} <- @edit_mappings do %>
          <div class="h-96 w-auto overflow-y-auto rounded-lg border-t-2 border-gray-300 bg-slate-50">
            <div class="sticky top-0 bg-white dark:bg-gray-700">
              <p class="text-sm text-gray-500 dark:text-brand-50">{fetch_from_lf_name(id)}</p>
              <input type="hidden" name="lf_id" value={id} />
              <div
                :if={@edit_mappings != %{}}
                class="flex grid grid-cols-7 gap-2 text-sm font-sans text-slate-400 font-semibold"
              >
                <p class=" col-span-2 pl-3 align-text-bottom">INCOMING</p>
                <p class="col-span-2 col-start-4 pl-3">STAGED FIELD</p>
                <div class="col-span-2 col-start-7">
                  <button type="submit" title="Save Mapping" class=" text-xs rounded-md p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      class="w-6 h-6 fill-blue-400 hover:fill-blue-600"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M9.75 6.75h-3a3 3 0 0 0-3 3v7.5a3 3 0 0 0 3 3h7.5a3 3 0 0 0 3-3v-7.5a3 3 0 0 0-3-3h-3V1.5a.75.75 0 0 0-1.5 0v5.25Zm0 0h1.5v5.69l1.72-1.72a.75.75 0 1 1 1.06 1.06l-3 3a.75.75 0 0 1-1.06 0l-3-3a.75.75 0 1 1 1.06-1.06l1.72 1.72V6.75Z"
                        clip-rule="evenodd"
                      />
                      <path d="M7.151 21.75a2.999 2.999 0 0 0 2.599 1.5h7.5a3 3 0 0 0 3-3v-7.5c0-1.11-.603-2.08-1.5-2.599v7.099a4.5 4.5 0 0 1-4.5 4.5H7.151Z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <%= for {incoming, mapped_to} <- mapping do %>
              <div class="flex grid grid-cols-7 gap-2 px-3">
                <div class="mt-2 p-1.5 text-gray-900 text-sm col-span-2">
                  <p name="incoming">{incoming}</p>
                  <input type="hidden" name="incoming_values[]" value={incoming} />
                </div>
                <div class="w-5 h-5 col-span-1 col-start-3 mt-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="size-6 fill-gray-500"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <div class=" col-start-4 col-span-2">
                  <div class="mt-2 text-sm p-1.5 rounded-md">
                    <select
                      id="staged_field"
                      name="mapping_pairs[]"
                      autocomplete="staged_field-name"
                      class="w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 "
                    >
                      <option>{mapped_to}</option>
                      <option :for={heading <- @staging_headings}>{heading}</option>
                    </select>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </form>
    <% end %>
    
<!-- Show Saved Mapping -->
    <%= if @saved_mappings != [%{}] do %>
      <%= for map <- @saved_mappings do %>
        <%= for {id, mapping} <- map do %>
          <%= if @edit != id do %>
            <div class="h-96 w-auto overflow-y-auto rounded-lg border-t-2 border-gray-300 bg-slate-50">
              <div class="sticky top-0 bg-white dark:bg-gray-700">
                <p class="text-sm text-gray-500 dark:text-brand-50">{fetch_from_lf_name(id)}</p>
                <input type="hidden" name="lf_id" value={map[:lead_file_id]} />
                <div class="">
                  <div
                    :if={@saved_mappings != [%{}]}
                    class="flex grid grid-cols-7 gap-2 text-sm font-sans text-slate-400 font-semibold"
                  >
                    <p class=" col-span-2 pl-3 align-text-bottom">INCOMING</p>
                    <p class="col-span-2 col-start-4 pl-3">STAGED FIELD</p>
                    <div class="col-span-1 col-start-7">
                      <.button
                        phx-click="edit_mapping"
                        phx-value-id={id}
                        title="Edit Mapping"
                        class=" rounded-md p-2 sticky bottom-0"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          class="w-5 h-5  fill-gray-300 hover:fill-gray-500 "
                        >
                          <path d="m5.433 13.917 1.262-3.155A4 4 0 0 1 7.58 9.42l6.92-6.918a2.121 2.121 0 0 1 3 3l-6.92 6.918c-.383.383-.84.685-1.343.886l-3.154 1.262a.5.5 0 0 1-.65-.65Z" />
                          <path d="M3.5 5.75c0-.69.56-1.25 1.25-1.25H10A.75.75 0 0 0 10 3H4.75A2.75 2.75 0 0 0 2 5.75v9.5A2.75 2.75 0 0 0 4.75 18h9.5A2.75 2.75 0 0 0 17 15.25V10a.75.75 0 0 0-1.5 0v5.25c0 .69-.56 1.25-1.25 1.25h-9.5c-.69 0-1.25-.56-1.25-1.25v-9.5Z" />
                        </svg>
                      </.button>
                    </div>
                  </div>
                </div>
              </div>
              <%= for {incoming, mapped_to} <- mapping do %>
                <div class="flex grid grid-cols-7 gap-2 px-3">
                  <div class="mt-2 p-1.5 text-gray-900 text-sm col-span-2">
                    <p name="incoming">{incoming}</p>
                    <input type="hidden" name="incoming_values[]" value={incoming} />
                  </div>
                  <div class="w-5 h-5 col-span-1 col-start-3 mt-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      class="size-6 fill-gray-500"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div class=" col-start-4 col-span-2">
                    <div class="mt-2 text-sm border p-1.5 border-gray-300 rounded-md bg-white text-gray-500">
                      <p class="truncate" name="staged_field">{mapped_to}</p>
                      <input type="hidden" name="mapping_pairs[]" value={mapped_to} />
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>
          <div
            :if={@saving_error != :ok and @saving == id}
            class="flex space-x-1 text-red-500 border-red-400 bg-red-200 py-1 px-2 rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
              />
            </svg>
            <p class=" font-normal text-xs">{@saving_error}</p>
          </div>
        <% end %>
      <% end %>
    <% end %>
  </:body>
</.panel>

<.modal :if={@uploaded} show={@uploaded} id="confirm-modal" on_cancel={JS.push("close-modal")}>
  <:title>Lead File Confirmation</:title>
  <:subtitle>
    <div>The following lead files have been uploaded and are ready for processing.</div>
    <div>
      This process may take several minutes depending on the number and size of the lead files.
    </div>
  </:subtitle>
  <div class="flex flex-col gap-4">
    <.lead_file_state_list files={@setup.lead_files} states={@states} />
    <span
      phx-click="plan"
      alt="Here be dragons"
      class={[
        "cursor-pointer text-center phx-submit-loading:opacity-75 rounded-lg py-2 px-3",
        "text-sm font-semibold leading-6",
        "bg-yellow-50 hover:bg-yellow-100  text-yellow-800 active:text-white/80",
        "ring-1 ring-inset ring-yellow-800/20 shadow-2xs drop-shadow-xs"
      ]}
      phx-disable-with="Creating Jobs..."
    >
      Confirm Lead Processing
    </span>
  </div>
</.modal>
