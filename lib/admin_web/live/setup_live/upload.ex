defmodule AdminWeb.SetupLive.Upload do
  use AdminWeb, :live_view
  use AdminWeb.SetupLive.SidebarHelpers

  require Logger

  @moduledoc """
  This live view process is responsible for the first step in the process taking files that are ready to send, and registering them in the system.

  This is done in multiple steps.
  1. Allow the user to upload whichever files we'd like to send.
  2. Process these files in a temp directory, and store the results in the session. Allow the user to pick the type of each file.
  3. Once verified, we upload the files to S3, and update their path.
  4. Stage the file, show the user the columns, allow the user to pick each column type if we guessed wrong.
  5. Analyze the file, store the SoruceMetrics, show them to the user.
  6. Allow the user to verify the file, and then send it.
  """

  require Logger

  alias Admin.Crm.Files
  alias Admin.Crm.LeadFile.LeadFileMapping
  alias Admin.Crm.Setups.{Setup, Validation}
  alias Admin.Crm.{Domain, LeadFile}
  alias Admin.Services.S3
  # alias Admin.Crm.List
  alias Explorer.{DataFrame, Series}
  alias Phoenix.LiveView.AsyncResult

  import AdminWeb.GadStyles.Components.Spinner

  require Explorer.DataFrame

  import AdminWeb.SetupLive.Components
  import AdminWeb.SetupLive.Helpers

  import Admin.Crm.Setups,
    only: [
      get_staging_headings: 0,
      get_saved_mappings: 1,
      fetch_unsaved_mappings: 2,
      check_duplication: 1,
      fetch_default_mappings: 2,
      mapping_data_pairs: 1
    ]

  require Ecto.Query
  import Ecto.Query

  # MARK: Mount
  @impl true
  def mount(%{"id" => id}, _session, socket) do
    setup = Ash.get!(Setup, id, load: [:lead_files])

    Admin.PubSubNotifier.subscribe(LeadFile, "update")

    {:ok,
     socket
     |> assign(
       setup: setup,
       states: build_states(setup.lead_files),
       uploaded: false,
       processing: nil,
       processed: [],
       ready_to_continue: false,
       staging_headings: get_staging_headings(),
       sample_data: %{},
       edit: "",
       saving_error: :ok,
       saving: "",
       edit_mappings: %{},

       # Async fields
       # use to replace default mapping
       saved_mappings: %{},
       saved_mappings_loaded: AsyncResult.loading(),
       # not_saved_default_mappings
       default_mappings: %{},
       default_mappings_loaded: AsyncResult.loading(),
       # Validation.validate_mappings(all_mappings_and_sampledata)
       warnings: %{},
       warnings_loaded: AsyncResult.loading(),
       all_saved?: false,
       all_saved_loaded: AsyncResult.loading()
     )
     |> allow_upload(:lead_files,
       accept: ~w(.txt .csv .xlsx .xls),
       max_entries: 10,
       max_file_size: 262_144_000
     )
     |> start_async(:mappings, fn -> fetch_mappings(setup) end)}
  end

  def fetch_mappings(setup) do
    all_mappings_and_sampledata = map_uploaded_files(setup.lead_files, get_staging_headings())

    not_saved_default_mappings =
      fetch_default_mappings(all_mappings_and_sampledata, setup.lead_files)

    Logger.debug("not_saved_default_mappings: #{inspect(not_saved_default_mappings)}")

    saved_mappings = get_saved_mappings(setup.lead_files)

    # Sort by ID
    not_saved_default_mappings =
      not_saved_default_mappings
      |> Enum.sort_by(fn map -> map["lf_id"] end)

    %{
      saved_mappings: saved_mappings,
      default_mappings: not_saved_default_mappings,
      warnings: Validation.validate_mappings(all_mappings_and_sampledata),
      all_saved?: if(%{} in saved_mappings, do: false, else: true)
    }
  end

  def handle_async(:mappings, {:ok, data}, socket) do
    {:noreply,
     socket
     |> assign(
       saved_mappings: data[:saved_mappings],
       saved_mappings_loaded: AsyncResult.ok(:ok),
       default_mappings: data[:default_mappings],
       default_mappings_loaded: AsyncResult.ok(:ok),
       warnings: data[:warnings],
       warnings_loaded: AsyncResult.ok(:ok),
       all_saved?: data[:all_saved?],
       all_saved_loaded: AsyncResult.ok(:ok)
     )}
  end

  # MARK: UI Callbacks
  @impl Phoenix.LiveView
  def handle_event("validate", params, socket) do
    socket =
      case params do
        %{"_target" => ["lead_files"]} ->
          socket

        %{"incoming_values" => incoming_values, "mapping_pairs" => mapped_values} ->
          new_mapping =
            Enum.zip(incoming_values, mapped_values)
            |> Enum.into(%{})
            |> Map.put("lf_id", params["lf_id"])

          duplicated_mappings? = check_duplication(mapped_values)

          Logger.debug("duplicated_mapping?: #{inspect(duplicated_mappings?)}")

          saving_error =
            case duplicated_mappings? do
              [] ->
                :ok

              _ ->
                dup_headings = Enum.map(duplicated_mappings?, fn {mapped, _} -> mapped end)
                "Duplicated mapped field/s found: #{Enum.map(dup_headings, &"#{&1} ")}"
            end

          remaining_mappings =
            Enum.map(
              socket.assigns.default_mappings,
              fn map ->
                if map["lf_id"] != params["lf_id"] do
                  map
                end
              end
            )
            |> Enum.filter(fn map -> not is_nil(map) end)

          previously_saved? = if params["lf_id"] == socket.assigns.edit, do: true, else: false

          updated_mapping = remaining_mappings ++ [new_mapping]

          # Sort by id
          updated_mapping =
            updated_mapping
            |> Enum.sort_by(fn map -> map["lf_id"] end)

          socket =
            if previously_saved? do
              assign(socket,
                edit_mappings: [{socket.assigns.edit, Map.pop(new_mapping, "lf_id") |> elem(1)}]
              )
            else
              assign(socket, default_mappings: updated_mapping)
            end

          # updated_mapping = if remaining_mappings == [nil], do: [new_mapping], else: remaining_mappings ++ new_mapping
          Logger.debug("updated_mapping: #{inspect(updated_mapping)}")

          socket = assign(socket, saving_error: saving_error, saving: params["lf_id"])

          socket

        _ ->
          socket
      end

    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "save_mapping",
        %{
          "incoming_values" => incoming_values,
          "mapping_pairs" => mapped_values,
          "lf_id" => lead_file_id
        } = params,
        socket
      ) do
    # Validate Mapped_to fields
    # - require a SourceCode| HomeState | HomePhone --> heading_mapped_to
    # - prevent heading_mapped_to: nil
    # - prevent dup field mappping

    duplicated_mappings? = check_duplication(mapped_values)

    # Logger.debug("duplicated_mapping?: #{inspect(duplicated_mappings?)}")

    # no unmapped fields
    result =
      if "" not in mapped_values do
        # missing required mapped fields
        if "homephone" not in mapped_values or "homestate" not in mapped_values or
             "sourcecode" not in mapped_values do
          {:error, "Required Mapped Fields not found (homephone, homestate, sourcecode)"}
        else
          if duplicated_mappings? != [] do
            dup_headings =
              Enum.map(duplicated_mappings?, fn {mapped, _} -> mapped end)

            {:error, "Duplicated mapped field/s found: #{Enum.map(dup_headings, &"#{&1} ")}"}
          else
            if socket.assigns.setup.do_rnd? and ("qual_date" in mapped_values or ("expire_date"  in mapped_values and "term" in mapped_values))  do
              saved_mapping =
                Enum.zip(incoming_values, mapped_values)
                |> Enum.into(%{})
                |> Enum.map(fn {incoming_value, mapped_value} ->
                  attrs = %{
                    heading_incoming: String.trim(incoming_value),
                    heading_mapped_to: mapped_value,
                    lead_file_id: lead_file_id
                  }

                  if incoming_value != "lead_file_name" do
                    LeadFileMapping
                    |> Ash.Changeset.for_create(:upsert, attrs, upsert?: true)
                    |> Ash.create!()
                  end

                  Logger.debug("atrrs: #{inspect(attrs)}")
                end)

              {:ok, ""}
            else
              {:error, "RND Opt-in Requires Mapped Field/s: 'qual_date' or both ['expire_date' and 'term']"}

            end
          end
        end
      else
        {:error, "Mapped fields can not be empty"}
      end

    saved_mappings = get_saved_mappings(socket.assigns.setup.lead_files)

    all_saved? =
      if(%{} in get_saved_mappings(socket.assigns.setup.lead_files), do: false, else: true)

    {r, msg} = result
    saving_error = if(r == :error, do: msg, else: r)

    Logger.debug("all saved?: #{inspect(all_saved?)}")
    Logger.debug("saving_error: #{inspect(saving_error)}")
    # Logger.debug("length s-mps: #{inspect(length(socket.assigns.saved_mappings))}")
    # Logger.debug("s-mps: #{inspect(get_saved_mappings(socket.assigns.setup.lead_files))}")

    socket =
      socket
      |> assign(
        saved_mappings: saved_mappings,
        all_saved?: all_saved?,
        edit: "",
        saving_error: saving_error,
        saving: lead_file_id
      )
      |> assign(
        default_mappings:
          fetch_unsaved_mappings(socket.assigns.default_mappings, socket.assigns.setup.lead_files)
      )

    # {:noreply, push_redirect(socket, to:  "/crm/setups/#{socket.assigns.setup.id}/upload")}
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def handle_event("edit_mapping", %{"id" => id} = params, socket) do
    # Logger.debug("wthh: #{inspect(id)}")

    saved_mapping =
      socket.assigns.saved_mappings
      |> Enum.find(fn map -> Map.has_key?(map, id) end)
      |> Map.get(id)

    # Logger.debug("paramsss: #{inspect(params)}")
    # Logger.debug("length s-mps: #{inspect(socket.assigns.saved_mappings)}")
    # Logger.debug("in edit: #{inspect(saved_mapping)}")

    {:noreply, assign(socket, edit_mappings: [{id, saved_mapping}], edit: id)}
  end

  defp insert_mapping(leadfile_id, heading_incoming, heading_mapped_to) do
    Ecto.Adapters.SQL.query!(
      Admin.AdminRepo,
      "INSERT INTO lead_file_mapping(lead_file_id, heading_incoming, heading_mapped_to) values (?, ?, ?)",
      [leadfile_id, heading_incoming, heading_mapped_to]
    )

    :ok
  end

  @impl Phoenix.LiveView
  def handle_event("upload", _params, %{assigns: %{setup: setup}} = socket) do
    uploaded_paths =
      consume_uploaded_entries(socket, :lead_files, fn %{path: path}, entry ->
        {:ok, local_temp_path} = Temp.path()
        :ok = File.cp(path, local_temp_path)

        # return
        {:ok, {local_temp_path, entry}}
      end)

    # [LeadFile | {:error, error_struct, entry_name}, ...]
    uploaded_files =
      Enum.map(uploaded_paths, fn {local_temp_path, entry} ->
        upload_and_create_file(setup, local_temp_path, entry, socket.assigns.current_user)
      end)

    # We are going to assume failed lead_files will be re-uploaded
    errors =
      Enum.map(uploaded_files, fn
        {:error, e, name} -> "Error loading #{inspect(name)}: #{e}"
        _ -> nil
      end)
      |> Enum.reject(&is_nil/1)

    setup = Ash.get!(Setup, setup.id, load: [:lead_files])

    # ---------------------------------------------------------------
    all_mappings_and_sampledata =
      map_uploaded_files(uploaded_files, socket.assigns.staging_headings)

    mapping_data_pairs = mapping_data_pairs(all_mappings_and_sampledata)

    warnings =
      Validation.validate_mappings(all_mappings_and_sampledata)

    case errors do
      [] ->
        {
          :noreply,
          socket
          |> assign(:setup, setup)
          |> assign(:states, build_states(setup.lead_files))
          |> put_flash(:info, "Files uploaded")
          # |> push_navigate(to: ~p"/crm/setups/#{setup.id}/plan")
        }

      errors ->
        {
          :noreply,
          socket
          |> assign(:setup, setup)
          |> assign(:states, build_states(setup.lead_files))
          |> assign(:error, "There was an error uploading some files. Error: #{errors}")
        }
    end

    Logger.debug(" DEFAULT: #{inspect(mapping_data_pairs)}")

    Logger.debug(" DEFAULT W: #{inspect(warnings)}")

    socket =
      socket
      |> assign(default_mappings: mapping_data_pairs, warnings: warnings)

    {:noreply, push_redirect(socket, to: "/crm/setups/#{setup.id}/upload")}
  rescue
    e ->
      throw(e)
      {:noreply, put_flash(socket, :error, "There was an error with the upload: #{inspect(e)}")}
  end

  # MARK: Helpers
  def map_uploaded_files(uploaded_files, staging_headings) do
    # Logger.debug("Uploaded Files: #{inspect(uploaded_files)}")

    # mapping_data_pairs =
    Enum.map(
      uploaded_files,
      fn uploaded_file ->
        {:ok, loaded_lead_file, data} = Files.load_lead_file(uploaded_file, %{})

        list =
          uploaded_file
          |> Admin.Crm.List.new(data)

        # Logger.debug("Loaded List Data: #{inspect(list)}")
        {lead_file_name, output} = extracting_file_data(list)

        # Logger.debug("File Extracted output: #{inspect(output)}")

        mapping = heading_mapping(output, staging_headings, lead_file_name)

        # Logger.debug("raw output #{inspect(output)}")

        # TODO: In prod, fetch as many rows as time allows.
        {mapping, Enum.take(output, 5000)}
      end
    )
  end

  def extracting_file_data(%{file: %LeadFile{id: lead_file_id} = lead_file} = list) do
    output =
      list.df
      |> DataFrame.to_rows()
      |> Enum.map(fn map ->
        map
        |> Enum.map(fn {key, value} -> {String.downcase(key), value} end)
        |> Enum.into(%{})
        |> Map.put("lead_file_id", lead_file_id)
      end)

    # returns a list of maps [%{},%{}]
    {lead_file.name, output}
  end

  def heading_mapping(extracted_data, headings_from_staging2, lead_file_name) do
    first_row = hd(extracted_data)
    {:ok, lf_id} = Map.fetch(first_row, "lead_file_id")

    # Logger.debug("extracted #{inspect(extracted_data)}")

    mapping = %{}
    # Track used new* fields to avoid duplicates
    used_new_fields = %{}

    file_mapping =
      Enum.reduce(first_row, {mapping, used_new_fields}, fn {h, v}, {acc, used_fields} ->
        found_heading = Enum.find(headings_from_staging2, fn sh -> sh == h end)

        found_heading2 =
          if is_nil(found_heading) do
            # Try to find a heading type based on patterns
            pattern_heading = find_heading_type(h)

            # If pattern matching didn't find a match, assign a new* field
            if is_nil(pattern_heading) do
              # Start with new21 and find the first available one
              next_new_field = find_next_available_new_field(used_fields)

              Logger.debug("Mapping unmapped field '#{h}' to '#{next_new_field}'")

              {next_new_field, Map.put(used_fields, next_new_field, true)}
            else
              {pattern_heading, used_fields}
            end
          else
            {found_heading, used_fields}
          end

        # Handle the tuple return from found_heading2
        {heading_value, updated_used_fields} = found_heading2

        # Logger.debug("updated_used_fields '#{inspect(updated_used_fields)}' ")
        {Map.put(acc, h, heading_value), updated_used_fields}
      end)

    # Extract the mapping from the tuple
    {final_mapping, _} = file_mapping

    final_mapping
    # |> Map.put("lead_file_name", lead_file_name)
    |> Map.put("lf_id", lf_id)
  end

  # Find the next available new* field starting from new21
  defp find_next_available_new_field(used_fields) do
    # Start with new21 and go up to new132
    result =
      Enum.find_value(21..132, fn num ->
        field_name = "new#{num}"

        if not Map.has_key?(used_fields, field_name) do
          field_name
        else
          nil
        end
      end)

    # In the unlikely case we've used all new* fields, return "misc1" as fallback
    result || "misc1"
  end

  def fetch_from_lf_name(lead_file_id) do
    Logger.debug("lf id display #{inspect(lead_file_id)}")

    lead_file_id = Admin.Integrations.DNCScrub.convert_uuid_tostring(lead_file_id)
    leadfile = Ash.get!(LeadFile, lead_file_id)

    %LeadFile{name: lf_name} = leadfile

    lf_name
  end

  @patterns %{
    ~r/customerid|customer id|fjid|account number|account num|acc#/ => "accountno",
    ~r/encrypted_id|encrypted_customer_id/ => "accountnumber2",
    ~r/first name|firstname|first_name/ => "fname",
    ~r/last name|lastname|last_name/ => "lname",
    ~r/middle name|middle initial/ => "minitial",
    ~r/address1|address 1|street name|street|street 1/ => "homeadd1",
    ~r/address2|address 2|street 2/ => "homeadd2",
    ~r/city|home city/ => "homecity",
    ~r/state|home state|region code|regioncode|region|state abbrev|state abbreviation/ =>
      "homestate",
    ~r/zip|zipcode|zip code|postal code|zip code_postal code/ => "homepostcode",
    ~r/country/ => "homecountry",
    ~r/phone|phone_no|phone number|phone num|phone_number|phone_num/ => "homephone",
    ~r/secondary phone|secondaryphone|secondary_phone/ => "altcompanyphone",
    ~r/cell phone|cellphone|mobile phone|mobile/ => "companyphone",
    ~r/email address|email_address/ => "email",
    ~r/company|company name/ => "companyname",
    ~r/source code|promo code|promocode|source/ => "sourcecode"
  }

  def find_heading_type(heading) do
    Enum.find_value(@patterns, fn {pattern, key} ->
      # Logger.debug("pattern #{inspect(pattern)}")
      if Regex.match?(pattern, heading), do: key, else: nil
    end)
  end

  @impl true
  def handle_event("plan", _payload, socket) do
    {
      :noreply,
      socket
      |> push_navigate(to: ~p"/crm/setups/#{socket.assigns.setup.id}/plan")
    }
  end

  def upload_and_create_file(setup, path, entry, current_user) do
    case S3.upload_lead_file(path, entry.client_name) do
      {:error, e} ->
        {:error, e, entry.client_name}

      {:ok, s3_path} ->
        setup
        |> create_lead_file(entry, s3_path, current_user)
    end
  end

  def create_lead_file(setup, entry, s3_path, current_user) do
    parsed_date =
      if entry.client_last_modified do
        entry.client_last_modified
        |> DateTime.from_unix!(:millisecond)
        |> DateTime.to_naive()
      else
        DateTime.now!("America/Denver")
        |> DateTime.to_naive()
      end

    file_options = %{
      "url" => "gad-s3://" <> s3_path,
      "url_migrated?" => true,
      "name" => entry.client_name,
      "file_format" => Path.extname(entry.client_name) |> _normalize_extension(),
      "file_size" => entry.client_size,
      "file_modify_date" => parsed_date,
      "setup_id" => setup.id
    }

    case Ash.Changeset.for_create(LeadFile, :upload, file_options, actor: current_user)
         |> Ash.create() do
      {:ok, lead_file} ->
        lead_file

      {:error, error} ->
        {:error, error, entry.client_name}
    end
  end

  # UI Helpers
  def maybe_put_flash(socket, files) when is_list(files) do
    case files do
      [_file] ->
        socket

      [_file | _rest] ->
        put_flash(
          socket,
          :info,
          "Files uploaded successfully, viewing the first file. Other files can be found as `uploaded` in the listing."
        )
    end
  end

  def file_title({%LeadFile{} = lead_file, _path}) do
    lead_file.name
  end

  def file_status(%{file: {%LeadFile{}, _path}} = assigns) do
    ~H"""
    <div class="">
      {@lead_file.name}
    </div>
    """
  end

  def error_to_string(:too_large), do: "Too large"
  def error_to_string(:not_accepted), do: "You have selected an unacceptable file type"
  def error_to_string(:too_many_files), do: "You have selected too many files"

  # Components
  @spec warnings(%{:warnings => any, optional(any) => any}) :: Phoenix.LiveView.Rendered.t()
  def warnings(%{warnings: []} = assigns) do
    ~H"""
    <span class="inline-flex items-center rounded-md bg-green-100 px-2.5 py-0.5 text-sm font-medium text-green-800">
      No warnings
    </span>
    """
  end

  def warnings(%{warnings: _warnings} = assigns) do
    ~H"""
    <div>
      <span class="inline-flex items-center rounded-md bg-pink-100 px-2.5 py-0.5 text-sm font-medium text-pink-800">
        {length(@warnings)} Warnings
      </span>
      <div class="mt-2">
        <ul class="list-disc list-inside">
          <%= for warning <- @warnings do %>
            <li>{warning}</li>
          <% end %>
        </ul>
      </div>
    </div>
    """
  end

  # TODO: Migrate this.
  def menu(assigns) do
    ~H"""
    <div class="lg:border-b lg:border-t lg:border-gray-200">
      <nav class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Progress">
        <ol
          role="list"
          class="overflow-hidden rounded-md lg:flex lg:rounded-none lg:border-l lg:border-r lg:border-gray-200"
        >
          <li class="relative overflow-hidden lg:flex-1">
            <div class="overflow-hidden border border-gray-200 rounded-t-md border-b-0 lg:border-0">
              <.step step_key="01" title="Upload" description="Upload file to S3" stage="completed" />
            </div>
          </li>
          <li class="relative overflow-hidden lg:flex-1">
            <div class="overflow-hidden border border-gray-200 lg:border-0">
              <.step
                step_key="02"
                title="Analyse"
                description="Gather metrics, break down by source"
                stage="active"
              />
              <.separator />
            </div>
          </li>
          <li class="relative overflow-hidden lg:flex-1">
            <div class="overflow-hidden border border-gray-200 rounded-b-md border-t-0 lg:border-0">
              <.step
                step_key="03"
                title="Verify"
                description="Verify all contacts in the file"
                stage="upcoming"
              />
              <.separator />
            </div>
          </li>
        </ol>
      </nav>
    </div>
    """
  end

  def separator(assigns) do
    ~H"""
    <!-- Separator -->
    <div class="absolute inset-0 left-0 top-0 hidden w-3 lg:block" aria-hidden="true">
      <svg
        class="h-full w-full text-gray-300"
        viewBox="0 0 12 82"
        fill="none"
        preserveAspectRatio="none"
      >
        <path
          d="M0.5 0V31L10.5 41L0.5 51V82"
          stroke="currentcolor"
          vector-effect="non-scaling-stroke"
        />
      </svg>
    </div>
    """
  end

  attr :step_key, :string
  attr :title, :string
  attr :description, :string
  attr :stage, :string, values: ["active", "completed", "upcoming"]

  def step(%{stage: "completed"} = assigns) do
    ~H"""
    <!-- Completed Step -->
    <a href="#" class="group">
      <span
        class="absolute left-0 top-0 h-full w-1 bg-transparent group-hover:bg-gray-200 lg:bottom-0 lg:top-auto lg:h-1 lg:w-full"
        aria-hidden="true"
      >
      </span>
      <span class="flex items-start px-6 py-5 text-sm font-medium">
        <span class="shrink-0">
          <span class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-600">
            <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path
                fill-rule="evenodd"
                d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
        </span>
        <span class="ml-4 mt-0.5 flex min-w-0 flex-col">
          <span class="text-sm font-medium">{@title}</span>
          <span class="text-sm font-medium text-gray-500">{@description}</span>
        </span>
      </span>
    </a>
    """
  end

  def step(%{stage: "active"} = assigns) do
    ~H"""
    <!-- Current Step -->
    <a href="#" aria-current="step">
      <span
        class="absolute left-0 top-0 h-full w-1 bg-indigo-600 lg:bottom-0 lg:top-auto lg:h-1 lg:w-full"
        aria-hidden="true"
      >
      </span>
      <span class="flex items-start px-6 py-5 text-sm font-medium lg:pl-9">
        <span class="shrink-0">
          <span class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-indigo-600">
            <span class="text-indigo-600">{@step_key}</span>
          </span>
        </span>
        <span class="ml-4 mt-0.5 flex min-w-0 flex-col">
          <span class="text-sm font-medium text-indigo-600">{@title}</span>
          <span class="text-sm font-medium text-gray-500">{@description}</span>
        </span>
      </span>
    </a>
    """
  end

  def step(%{stage: "upcoming"} = assigns) do
    ~H"""
    <!-- Upcoming Step -->
    <a href="#" class="group">
      <span
        class="absolute left-0 top-0 h-full w-1 bg-transparent group-hover:bg-gray-200 lg:bottom-0 lg:top-auto lg:h-1 lg:w-full"
        aria-hidden="true"
      >
      </span>
      <span class="flex items-start px-6 py-5 text-sm font-medium lg:pl-9">
        <span class="shrink-0">
          <span class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-gray-300">
            <span class="text-gray-500">{@step_key}</span>
          </span>
        </span>
        <span class="ml-4 mt-0.5 flex min-w-0 flex-col">
          <span class="text-sm font-medium text-gray-500">{@title}</span>
          <span class="text-sm font-medium text-gray-500">{@description}</span>
        </span>
      </span>
    </a>
    """
  end

  def _normalize_extension("." <> ext), do: _normalize_extension(ext)

  def _normalize_extension(ext) do
    case String.match?(ext, ~r/^\d{3}$/) do
      true ->
        "txt"

      false ->
        ext
    end
  end
end
