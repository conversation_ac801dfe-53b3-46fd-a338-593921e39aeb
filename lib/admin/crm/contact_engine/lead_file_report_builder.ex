defmodule Admin.Crm.ContactEngine.LeadFileReportBuilder do
  alias Admin.Crm.ContactEngine.LeadFileReport
  require Ash.Query
  require Ecto.Query
  require Logger

  # Very painful process
  def build_report(lead_file_id) do
    contacts = get_contacts_for(lead_file_id)

    by_source =
      contacts
      |> Enum.group_by(& &1.sourcecode)

    stats_by_source =
      by_source
      |> Map.keys()
      |> Enum.map(fn source ->
        source_contacts =
          by_source
          |> Map.get(source)

        count =
          source_contacts
          |> length()

        found_phones =
          source_contacts
          |> Enum.map(& &1.phones_metadata.found_phone_count)
          |> Enum.sum()

        valid_phones =
          source_contacts
          |> Enum.map(& &1.phones_metadata.unique_valid_phone_count)
          |> Enum.sum()

        lost_names =
          source_contacts
          |> Enum.filter(fn contact ->
            any? =
              contact.phones_metadata.all_phones
              |> Enum.any?(& &1.included?)

            not any?
          end)

        lost_numbers =
          source_contacts
          |> Enum.map(fn contact ->
            contact.phones_metadata.all_phones
            |> Enum.filter(&(not &1.included?))
          end)
          |> List.flatten()

        numbers_lost_by_reason =
          lost_numbers
          |> Enum.group_by(& &1.exclusion_reason)

        numbers_lost_breakdown =
          numbers_lost_by_reason
          |> Map.keys()
          |> Enum.map(fn source ->
            {
              source,
              numbers_lost_by_reason
              |> Map.get(source)
              |> length()
            }
          end)
          |> Enum.into(%{})

        names_lost_breakdown_by_reason =
          lost_names
          |> Enum.map(fn contact ->
            # Why did we loose this record?
            metadata = contact.phones_metadata.all_phones

            # metadata
            # |> Enum.map(& {contact.id, &1.exclusion_reason})
            # |> IO.inspect(label: "Why this number was excluded")

            cond do
              metadata
              |> Enum.any?(&(&1.exclusion_reason == "Invalid Number")) ->
                "Invalid Number"

              metadata
              |> Enum.any?(
                &(&1.exclusion_reason
                  |> String.downcase()
                  |> String.contains?("litigator"))
              ) ->
                # Logger.debug("decided on litigator: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}")
                "Known Litigator"

              metadata
              |> Enum.any?(
                &(&1.exclusion_reason
                  |> String.downcase()
                  |> String.contains?("blocked"))
              ) ->
                # Logger.debug("decided on blocked: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}")
                "Blocked from DNC DB Access"

              metadata
              |> Enum.any?(&(&1.exclusion_reason |> String.downcase() |> String.contains?("dnc"))) ->
                # Logger.debug("decided on dnc: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}")
                "DNC"

              metadata
              |> Enum.any?(
                &(&1.exclusion_reason
                  |> String.downcase()
                  |> String.contains?("no wireless state"))
              ) ->
                # Logger.debug("decided on no wireless: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}")

                "Wireless in a No Wireless State"

              not (metadata |> Enum.any?(& &1.valid)) ->
                # Logger.debug("decided on no valid numbers: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}")

                "No valid phone numbers"

              metadata
              |> Enum.any?(
                &(&1.exclusion_reason
                  |> String.downcase()
                  |> String.contains?("state match"))
              ) ->
                "Wireless in an excluded state"

              metadata
              |> Enum.any?(
                &(&1.exclusion_reason
                  |> String.downcase()
                  |> String.contains?("reassigned phone"))
              ) ->
                "Reassigned Phone"

              true ->
                Logger.warning(
                  "Fell through lead file report decision tree: #{inspect(metadata |> Enum.map(& &1.exclusion_reason))}"
                )

                # "No clue..."
                "Unmapped Reason"
            end
          end)
          |> Enum.group_by(& &1)

        names_lost_breakdown =
          names_lost_breakdown_by_reason
          |> Map.keys()
          |> Enum.map(fn reason ->
            cnt =
              names_lost_breakdown_by_reason
              |> Map.get(reason)
              |> length()

            {reason, cnt}
          end)
          |> Enum.into(%{})

        %{
          lead_file_id: lead_file_id,
          source_code: source,
          gross_names: count,
          net_names:
            source_contacts
            |> Enum.filter(fn contact ->
              contact.phones_metadata.all_phones
              |> Enum.any?(& &1.included?)
            end)
            |> length(),
          names_processed: count,
          numbers_processed: found_phones,
          average_gross_numbers_per_name: found_phones / count,
          average_net_numbers_per_name: valid_phones / count,
          names_lost_count: lost_names |> length(),
          numbers_lost_count: lost_numbers |> length(),
          numbers_lost: numbers_lost_breakdown,
          names_lost: names_lost_breakdown
        }
      end)

    stats_by_source
    |> create_or_replace_report(lead_file_id)
  end

  defp create_or_replace_report(stats_by_source, lead_file_id) do
    LeadFileReport
    |> Ash.Query.filter(lead_file_id == ^lead_file_id)
    |> Ash.bulk_destroy!(:destroy, %{})

    result =
      stats_by_source
      |> Ash.bulk_create!(LeadFileReport, :create, return_records?: true)

    {:ok, result.records}
  end

  defp get_contacts_for(lead_file_id) do
    Ecto.Query.from(c in Admin.Crm.LeadFile.StagedContact,
      where: c.lead_file_id == ^lead_file_id,
      select: c
    )
    |> Admin.AdminRepo.all()
  end
end
