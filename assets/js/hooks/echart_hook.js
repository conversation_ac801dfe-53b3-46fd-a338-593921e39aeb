import { Formatters } from "../formatters.js";


async function makeChart(el) {
    if (!window.ECharts) {
        const ECharts = await import("../../vendor/echarts.js");
        window.ECharts = ECharts
    }

    let chart = window.ECharts.init(el);
    return chart;
}

function replace_formatters(obj) {
    if (typeof obj === "string") {
        // Change the regex if you need a more permissive match.
        const match = obj.match(/^::([A-z]+)::$/);
        if (match && Formatters[match[1]]) {
            return Formatters[match[1]];
        }
        return obj;
    } else if (Array.isArray(obj)) {
        return obj.map(replace_formatters);
    } else if (obj && typeof obj === "object") {
        const newObj = {};
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                newObj[key] = replace_formatters(obj[key]);
            }
        }
        return newObj;
    }
    return obj;
}

export const EChartHook = {
    async mounted() {
        // Store the batch ID for comparison
        this.refId = this.el.dataset.refId;
        this.template = this.unpack(this.el.dataset.template)

        // Listen for chart events for this chart type
        this.handleEvent(`chart-${this.chartType}`, (options) => {
            this.updateOptions(this.el, options);
        });

        console.log(`EChart hook mounted for ${this.chartType}, ref ID: ${this.refId}`);
        // Create an empty chart initially to ensure the container is ready
        // This will be replaced when data arrives

        try {
            this.chart = await makeChart(this.el);
            window.patchart = this.chart;
            if (this.template) {
                this.chart.setOption(this.template);
            }
        } catch (error) {
            console.error(`Error rendering initial chart ${this.chartType}:`, error);
        }
    },

    destroyed() {
        if (this.chart) {
            console.log(`Destroying chart: ${this.chartType}`);
            this.chart.destroy();
        }
    },

    updated() {
        // Check if the batch ID has changed
        const newBatchId = this.el.dataset.batchId;
        if (this.batchId !== newBatchId) {
            console.log(`Batch ID changed from ${this.batchId} to ${newBatchId} for ${this.chartType}`);

            // Update the stored batch ID
            this.batchId = newBatchId;

            // If we have a chart, destroy it so it can be recreated with new data
            if (this.chart) {
                this.chart.destroy();
                this.chart = null;
            }
        }
    },

    async updateOptions(el, options) {
        console.log(`Updating chart ${this.chartType} with new options`, options);

        // Store options in window for debugging
        window[this.el.id] = options;

        this.chart.setOption(options);
    },

    unpack(template) {
        try {
            return JSON.parse(atob(template))
        } catch (e) {
            console.log(`An error occurred: ${e.toString()}`)
        }
    }
};