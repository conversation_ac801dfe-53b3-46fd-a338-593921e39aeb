defmodule Admin.Integrations.DNCScrub do
  @moduledoc """
  This module is responsible for scrubbing phone numbers against the DNCScrub API.

  Requests are limited to 10,000 clean phone numbers per request, and up to 100 requests per minute.

  Timeouts are proportional to the number of phone numbers being scrubbed. The best practices documentation
  recommends 4 seconds of timeout for every lead up to 200 leads, more than 200 leads should have a 600 second
  timeout (10 minutes).

  The SFTP server is used to do bulk scrubbing, and the API is used for individual phone number scrubbing.

  The SFTP server will normally scan the directory every 20 minutes, but we can call /app/main/rpc/ftpUploadComplete
  to force a scan manually.

  Scrubs uploaded to the SFTP server must be downloaded from the server; you cannot upload via sftp and then
  fetch via the API.

  The SFTP scrub will take an amount of time proportional to the number of phone numbers being scrubbed. All
  files submitted to the SFTP server must bs CSV or TXT format, and must be zipped with a .zip extension.

  ## SFTP Scrub

  The process looks like this:

  1. User selects list to scrub
  2. CRM Builds a CSV of phone numbers, fields (secondary, tertiary, etc), and DialIDs
  3. CRM zips the CSV w/ Admin.Integrations.DNCScrub.ZipHelper
  4. CRM Uploads the zip file to the SFTP server
    a. The directory will be /ProjectID/_Upload
    b. If a ProjectID is not used, use /_Upload, and the Master campaign will be used
  5. An amount of time passes
  6. CRM downloads the scrubbed file from the SFTP server
    a. The directory will be /ProjectID/_Download
    b. If a ProjectID is not used, use /_Download, and the Master campaign will be used
  7. CRM unzips the file:
    a. If the file is readable, the ZIP will be uploaded to S3, a link retained, and the file deleted
  from the SFTP server.
    b. If the file is not readable, we will attempt to re-download the file 3 times, then give up and
    notify the user that the scrub failed.

  ## Result Code Breakdown

  All results will have a Result Code, this will be one of the following:

  - B: Blocked
  - C: Clean
  - D: DNC Match
  - E: EBR - Currently valid, can be called
  - F: Valid EBR & Wireless, but can not be called because they
  are in a state that does not allow telemarketing.
  - G: Valid EBR & Wireless. Can be dailed manually.
  - H: Valid EBR & Wireless, would otherwise be DNC.
  - I: Invalid, not a real number
  - L: Wireless number in a state that does not allow telemarketing
  - M: Malformed, never happens though.
  - O: EBR Override applied to a DNC, can be called.
  - P: Project DNC (Internal DNC)
  - R: Expired EBR, would be EBR but expired. Not callable for residential.
  - V: Valid EBR overriding a DNC that is also wireless and in a state that does not allow telemarketing.
  - W: US Wireless number
  - X: Industry Exception, can be called
  - Y: Voip number, can be called
  """
  require Logger

  alias Admin.Crm.LeadFile.SourceMetrics
  alias Admin.Crm.LeadFile.StagedContact
  alias Admin.Crm.LeadFile.StagedContacts
  alias Admin.Crm.List
  alias Admin.Integrations.DNCScrub.{ExternalAPI, ExternalSFTP, ScrubResult}
  alias Admin.Services.S3API

  require Ecto.Query
  import Ecto.Query
  alias Admin.AdminRepo

  def api_impl, do: Application.get_env(:admin, :dnc_scrub_api, ExternalAPI)
  def sftp_impl, do: Application.get_env(:admin, :dnc_scrub_sftp, ExternalSFTP)
  def s3_impl, do: Application.get_env(:admin, :dnc_scrub_s3, S3API)

  def poll_sftp, do: api_impl().poll_sftp()

  def scrub_file(name, data, target \\ "/_Upload"), do: sftp_impl().scrub_file(name, data, target)

  def download(list_id, local_file, target \\ "/_Output"),
    do: sftp_impl().download(list_id, local_file, target)

  def delete(list_id, target \\ "/_Output"), do: sftp_impl().delete(list_id, target)

  def upload_scrub_result_archive(zip_path, list_id),
    do: s3_impl().upload_dnc_scrub_result_archive(zip_path, list_id)

  def download_scrub_result_archive(path), do: s3_impl().download_dnc_scrub_result_archive(path)

  @doc """
  Updates the main DNC, this includes records that should not be called under any settings.

  This will update *_dnc? if core dnc is true, and *_line_type
  Update landline_count and wireless_count accountingly.

  Though it updates all line types, it returns the total number of DNC phone numbers.

  For the 2nd, NN Scrub, see update_dnc_nn/2
  """
  @deprecated "use ContactEngine.Helpers.apply_dnc_results/2 instead"
  def update_dnc_core(
        lf_id,
        nn?,
        fields \\ ~w(homephone newphone companyphone altcompanyphone)a
      ) do
    fields1 = StagedContacts.phonefields_staged(lf_id)
    lead_file_id = string_to_uuid(lf_id)

    result =
      fields1
      |> Enum.map(fn field ->
        # Apply line type
        apply_linetype_to_staged_lead_file(lead_file_id, field, nn?)

        # Processor 2.0: Update lead file source metrics - on all phone types
        # fetch_leadfile_sourcemetrics(field, lead_file_id)

        # Mark Phones as DNC
        case apply_scrub_to_staged_lead_file(lead_file_id, field, nn?) do
          {updated, _} -> updated
          0 -> 0
          _ -> 0
        end
      end)
      |> Enum.sum()

    # Update lead file source metrics - on home phone only
    fetch_leadfile_sourcemetrics(:homephone, lead_file_id, nn?)

    {:ok, result}
  end

  @acceptable_update_fields [
    :homephone_dnc?,
    :newphone_dnc?,
    :companyphone_dnc?,
    :altcompanyphone_dnc?
  ]
  def apply_scrub_to_staged_lead_file(lead_file_id, field, nn? \\ false) do
    result_code_field = (nn? && :result_code_nn) || :result_code

    dnc_field =
      field
      |> Atom.to_string()
      |> then(&(&1 <> "_dnc?"))
      |> String.to_atom()

    from(c in "contact_staging",
      join: d in "dnc_registry",
      on: d.phone_number == field(c, ^field),
      # or *_nn == "D"
      # field(d, ^result_code_field) not in ["C", "B", "W", "Y"] ,
      where:
        (d.result_code not in ["C", "B", "W", "Y"] or
           field(d, ^result_code_field) not in ["C", "B", "W", "Y"]) and
          c.lead_file_id == ^lead_file_id,
      update: [set: [{^dnc_field, true}]]
    )
    |> AdminRepo.update_all([], timeout: :infinity)
  end

  @acceptable_update_fields [
    :homephone_line_type,
    :newphone_line_type,
    :companyphone_line_type,
    :altcompanyphone_line_type
  ]
  def apply_linetype_to_staged_lead_file(lead_file_id, field, nn? \\ false) do
    result_code_field = :result_code

    line_type_field =
      field
      |> Atom.to_string()
      |> then(&(&1 <> "_line_type"))
      |> String.to_atom()

    _apply_line_type(
      lead_file_id,
      line_type_field,
      field,
      result_code_field,
      ["C", "B"],
      "landline"
    )

    _apply_line_type(lead_file_id, line_type_field, field, result_code_field, ["W"], "wireless")
    _apply_line_type(lead_file_id, line_type_field, field, result_code_field, ["Y"], "voip")

    if nn? do
      result_code_field = :result_code_nn

      _apply_line_type_w(
        lead_file_id,
        line_type_field,
        field,
        result_code_field,
        ["W"],
        "wireless"
      )

      _apply_line_type_w(lead_file_id, line_type_field, field, result_code_field, ["Y"], "voip")

      # Updates line_type to 'Uncallable' if result_code_nn code is not valid
      _apply_line_type_w_uncallable(
        lead_file_id,
        line_type_field,
        field,
        result_code_field,
        ["C", "W", "Y"],
        "Uncallable"
      )
    end

    update_dialer_counts(lead_file_id, "landline", ["landline"], line_type_field)
    update_dialer_counts(lead_file_id, "wireless", ["wireless", "voip"], line_type_field)
  end

  def _apply_line_type(
        lf_id,
        line_type_field,
        phone_field,
        result_code_field,
        result_codes,
        value
      ) do
    from(c in "contact_staging",
      join: d in "dnc_registry",
      on: d.phone_number == field(c, ^phone_field),
      where: c.lead_file_id == ^lf_id and field(d, ^result_code_field) in ^result_codes,
      update: [set: [{^line_type_field, ^value}]]
    )
    |> AdminRepo.update_all([], timeout: :infinity)
  end

  def _apply_line_type_w_uncallable(
        lf_id,
        line_type_field,
        phone_field,
        result_code_field,
        result_codes,
        value
      ) do
    from(c in "contact_staging",
      join: d in "dnc_registry",
      on: d.phone_number == field(c, ^phone_field),
      where:
        c.lead_file_id == ^lf_id and field(d, ^result_code_field) not in ^result_codes and
          d.result_code not in ["C", "B"],
      update: [set: [{^line_type_field, ^value}]]
    )
    |> AdminRepo.update_all([], timeout: :infinity)
  end

  def _apply_line_type_w(
        lf_id,
        line_type_field,
        phone_field,
        result_code_field,
        result_codes,
        value
      ) do
    from(c in "contact_staging",
      join: d in "dnc_registry",
      on: d.phone_number == field(c, ^phone_field),
      where:
        c.lead_file_id == ^lf_id and field(d, ^result_code_field) in ^result_codes and
          d.result_code not in ["C", "B"],
      update: [set: [{^line_type_field, ^value}]]
    )
    |> AdminRepo.update_all([], timeout: :infinity)
  end

  def update_dialer_counts(lead_file_id, dialer, types, line_type_field) do
    dialer_type_field =
      dialer
      |> then(&(&1 <> "_count"))
      |> String.to_atom()

    result =
      from(c in "contact_staging",
        where: c.lead_file_id == ^lead_file_id and field(c, ^line_type_field) in ^types,
        select: [c.id, count(c)],
        group_by: c.id
      )
      |> AdminRepo.all()

    updated_results =
      result
      |> Enum.map(fn [id, count] ->
        # formattedid = convert_uuid_tostring(id)

        from(c in "contact_staging",
          where: field(c, ^line_type_field) in ^types and c.id == ^id,
          update: [
            set: [{^dialer_type_field, fragment("? + ?", field(c, ^dialer_type_field), ^count)}]
          ]
        )
        |> AdminRepo.update_all([])
      end)

    Logger.debug("dialer count result: #{inspect(updated_results)}")
  end

  def download_and_parse_results(full_scrub? \\ true, lead_file_id, target \\ "/_Output") do
    with {:ok, dir} <- Temp.mkdir(),
         {:ok, path} <- download(lead_file_id, dir <> "/output.zip", target),
         {:ok, zip_data} <- Elixir.File.read(path),
         {:ok, results} <- Admin.Integrations.DNCScrub.ZipHelper.unzip(zip_data),
         results <- Map.get(results, "detailed.txt"),
         {:ok, scrub_results} <-
           Admin.Integrations.DNCScrub.ScrubResult.parse(results, NaiveDateTime.local_now()) do
      if full_scrub? do
        # 1. Save all DNC results to registry
        %Ash.BulkResult{status: :success} =
          scrub_results
          |> Ash.bulk_create(ScrubResult, :create,
            upsert?: true,
            upsert_identity: :unique_phone
          )
      else
        # Updates dnc_registry[result_code_nn] for nn / wireless / viop numbers.
        :ok =
          Enum.each(
            scrub_results,
            fn map ->
              num = Map.get(map, "phone_number")
              nn_code = Map.get(map, "result_code")

              # TODO?: Potentually move the update out a layer, and update a whole batch at once.
              res =
                from(
                  d in "dnc_registry",
                  where: d.phone_number == ^num,
                  update: [set: [result_code_nn: ^nn_code]]
                )
                |> AdminRepo.update_all([])

              # Logger.debug("DNC Registry Update Result: #{inspect(res)}")
            end
          )

        # Logger.debug("detailed.txt nn results: #{inspect({phone_number, result_code})}")
      end

      # Save the results and upload them to s3 for later review
      Elixir.File.write(dir <> "/results.zip", zip_data)
      {:ok, path} = upload_scrub_result_archive(dir <> "/results.zip", lead_file_id)

      # Clean up after ourselves.
      :ok = delete(lead_file_id, target)

      {:ok, scrub_results, path}
    end
  end

  # 1.0: Only on homephone
  def fetch_leadfile_sourcemetrics(phone_field, current_leadfile_id, nn?) do
    results =
      from(c in "contact_staging",
        where: c.lead_file_id == ^current_leadfile_id,
        group_by: [c.sourcecode, c.lead_file_id],
        select: %{
          source: c.sourcecode,
          records_scrubbed: count(c),
          clean: sum(fragment("CASE WHEN ? = 'C' THEN 1 ELSE 0 END", c.homephone_result_code)),
          invalid: sum(fragment("CASE WHEN ? = 'I' THEN 1 ELSE 0 END", c.homephone_result_code)),
          blocked: sum(fragment("CASE WHEN ? = 'B' THEN 1 ELSE 0 END", c.homephone_result_code)),
          ebr: sum(fragment("CASE WHEN ? = 'E' THEN 1 ELSE 0 END", c.homephone_result_code)),
          wireless: sum(fragment("CASE WHEN ? = 'W' THEN 1 ELSE 0 END", c.homephone_result_code)),
          viop: sum(fragment("CASE WHEN ? = 'Y' THEN 1 ELSE 0 END", c.homephone_result_code)),
          dnc: sum(fragment("CASE WHEN ? = 'D' THEN 1 ELSE 0 END", c.homephone_result_code)),
          malformed:
            sum(fragment("CASE WHEN ? = 'M' THEN 1 ELSE 0 END", c.homephone_result_code)),
          lost_records:
            sum(
              fragment(
                "CASE WHEN (? NOT IN ('C','B','W','Y')) OR (? IS NULL) THEN 1 ELSE 0 END",
                c.homephone_result_code,
                c.homephone
              )
            ),
          ebr_w_uncallable_state:
            sum(fragment("CASE WHEN ? = 'F' THEN 1 ELSE 0 END", c.homephone_result_code)),
          ebr_w_manual:
            sum(fragment("CASE WHEN ? = 'G' THEN 1 ELSE 0 END", c.homephone_result_code)),
          ebr_w_dnc:
            sum(fragment("CASE WHEN ? = 'H' THEN 1 ELSE 0 END", c.homephone_result_code)),
          override_EBR:
            sum(fragment("CASE WHEN ? = 'O' THEN 1 ELSE 0 END", c.homephone_result_code)),
          expired_EBR:
            sum(fragment("CASE WHEN ? = 'R' THEN 1 ELSE 0 END", c.homephone_result_code)),
          # valid ebr overriding dnc in uncallable state
          code_V_EBR:
            sum(fragment("CASE WHEN ? = 'V' THEN 1 ELSE 0 END", c.homephone_result_code)),
          # wireless_uncallable_state
          w_uncallable_state:
            sum(fragment("CASE WHEN ? = 'L' THEN 1 ELSE 0 END", c.homephone_result_code)),
          # internal_dnc
          internal_dnc:
            sum(fragment("CASE WHEN ? = 'P' THEN 1 ELSE 0 END", c.homephone_result_code)),
          # industry_exception
          industry_exception:
            sum(fragment("CASE WHEN ? = 'X' THEN 1 ELSE 0 END", c.homephone_result_code)),
          rnd:
            sum(fragment("CASE WHEN jsonb_extract_path(?, 'all_phones','0','rnd?')::boolean = true' THEN 1 ELSE 0 END", c.phones_metadata)),
          lead_file_id: c.lead_file_id
        }
      )
      |> AdminRepo.all()

    case is_nil(results) do
      true ->
        nil

      false ->
        results
        |> Enum.map(fn result ->
          update_leadfile_sourcemetrics(result, current_leadfile_id, phone_field)
        end)
    end

    Logger.debug("metrics result: #{inspect(results)}")
  end

  def update_leadfile_sourcemetrics(metrics, current_leadfile_id, phone_field) do
    # Processor 2.0: Summate metrics categories over all phone types
    # case is_nil(scourcemetricid_exists?(uuid)) do
    # true ->
    SourceMetrics
    |> Ash.Changeset.for_create(:upsert, metrics, upsert?: true)
    |> Ash.create!()

    #   false ->
    #     metrics
    #     |> Map.from_struct()
    #     |>Enum.map(
    #       fn {field, value} ->
    #         if field == :clean or field == :blocked or field == :wireless
    #             or field == :viop or field == :invalid or field == :ebr or field == :dnc
    #             or field == :malformed or field == :lost_records or field == :records_scrubbed
    #         do

    #           from(
    #             m in "lead_file_source_metrics",
    #             where: m.lead_file_id == ^uuid,
    #             update: [set: [{^field, fragment("? + ?", field(m, ^field), ^value) }]]
    #           )|> AdminRepo.update_all([])
    #         end

    #       end
    #     )
    # end
  end

  def get_leadfile_sourcemetrics(lead_file_id) when not is_nil(lead_file_id) do
    {_, uuid} = Ecto.UUID.dump(lead_file_id)

    from(
      m in "lead_file_source_metrics",
      where: m.lead_file_id == ^uuid,
      select: %{
        source: m.source,
        records_scrubbed: m.records_scrubbed,
        clean: m.clean,
        invalid: m.invalid,
        blocked: m.blocked,
        ebr: m.ebr,
        wireless: m.wireless,
        viop: m.viop,
        dnc: m.dnc,
        malformed: m.malformed,
        ebr_w_uncallable_state: m.ebr_w_uncallable_state,
        ebr_w_manual: m.ebr_w_manual,
        ebr_w_dnc: m.ebr_w_dnc,
        override_EBR: m.override_EBR,
        expired_EBR: m.expired_EBR,
        code_V_EBR: m.code_V_EBR,
        w_uncallable_state: m.w_uncallable_state,
        internal_dnc: m.internal_dnc,
        industry_exception: m.industry_exception,
        lost_records: m.lost_records
      }
    )
    |> AdminRepo.all()
  end

  def get_leadfile_overall_metrics(lead_file_id) when not is_nil(lead_file_id) do
    get_leadfile_sourcemetrics(lead_file_id)
    |> Enum.reduce(%{}, fn map, acc ->
      {_, updated_map} = Map.pop(map, :source)

      Map.merge(acc, updated_map, fn _key, acc_val, map_val ->
        acc_val + map_val
      end)
    end)
  end

  def scourcemetricid_exists?(current_leadfile_id) do
    id =
      from(
        m in "lead_file_source_metrics",
        where: m.lead_file_id == ^current_leadfile_id,
        select: m.lead_file_id
      )
      |> Admin.AdminRepo.one()

    id
  end

  def get_initial_wireless_bysource_nn(lead_file_id, source) do
    {_, uuid} = Ecto.UUID.dump(lead_file_id)

    initial_wireless_counts =
      from(
        c in "contact_staging",
        left_join: d in "dnc_registry",
        # field(c, ^phone_field),
        on: d.phone_number == c.homephone,
        where: c.lead_file_id == ^uuid and c.sourcecode == ^source,
        group_by: [c.sourcecode, c.lead_file_id],
        select: sum(fragment("CASE WHEN ? IN ('W')  THEN 1 ELSE 0 END", d.result_code))
      )
      |> Admin.AdminRepo.one()

    initial_wireless_counts
  end

  ## build nn file - Export all records from initial scrub by dnc_registry[result_code]
  ## and ask ContactEngine to build the file according to the dial policy
  @deprecated "use ZipHelper.build_second_scrub_file/2 instead"
  def build_second_scrub_file(
        lead_file_id,
        dial_policy,
        fields \\ ~w(homephone newphone companyphone altcompanyphone)a
      ) do
    {_, uuid} = Ecto.UUID.dump(lead_file_id)

    all_result_codes =
      fields
      |> Enum.map(fn f ->
        from(
          c in "contact_staging",
          join: d in "dnc_registry",
          on: d.phone_number == field(c, ^f),
          where: c.lead_file_id == ^uuid,
          select: {field(c, ^f), d.result_code}
        )
        |> AdminRepo.all()
      end)
      |> Elixir.List.flatten()
      |> Enum.uniq_by(fn {phone, _result_code} -> phone end)

    all_contacts =
      from(
        c in StagedContact,
        where: c.lead_file_id == ^uuid,
        select: c
      )
      |> AdminRepo.all()

    result =
      ContactEngine.Helpers.select_second_scrub_phones(
        all_contacts,
        all_result_codes,
        dial_policy
      )

    result
  end

  # #ignore
  def convert_uuids_tostring(result) do
    # convert uuids
    out =
      result
      |> Enum.map(fn [id, res] ->
        case Ecto.UUID.cast(res) do
          {:ok, uuid} ->
            [id, uuid]

          :error ->
            Logger.warning("Error in casting uuids")
        end
      end)

    out
  end

  def convert_uuid_tostring(uuid) do
    # convert uuid
    out =
      case Ecto.UUID.cast(uuid) do
        {:ok, uuid_str} ->
          uuid_str

        :error ->
          Logger.warning("Error in casting uuid")
      end

    out
  end

  def string_to_uuid(lf_id) do
    uuid =
      if is_binary(lf_id) do
        {_, uuid} = Ecto.UUID.dump(lf_id)
        uuid
      else
        lf_id
      end
  end
end
