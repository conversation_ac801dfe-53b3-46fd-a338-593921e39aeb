defmodule Admin.Integrations.RND do
  @moduledoc """
  This module represents a RND Integration.

  Helpers can be found at `Admin.Integrations.RND.Helpers`
  SFTP can be found at `Admin.Integrations.RND.ExternalSFTP`
  Test SFTP is mocked.
  """

  alias Admin.Crm.LeadFile.{StagedContact, BulkPhoneMetadata}
  alias Admin.Integrations.RND.{Helpers, ExternalSFTP}

  require Ash.Query

  # SFTP Env
  def sftp_impl, do: Application.get_env(:admin, :rnd_scrub_sftp, ExternalSFTP)

  # Upload RND File
  def  upload_rnd_file(name, file), do: sftp_impl().upload_rnd_file(name, file)

  # Poll SFTP
  def poll_sftp(list_id), do: sftp_impl().poll_sftp(list_id)

  # Download
  def download(list_id),
    do: sftp_impl().download(list_id)

  # Delete
  def delete(list_id, target \\ "/Results?"), do: sftp_impl().delete(list_id, target)

  def download_and_interpret_rnd_results(list, lf_id) do
    with {:ok, rnd_results} <- download(lf_id) do
      #Results Struct Eg.
      # {:ok,
      #  [
      #    {"7194333460", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"}
      #  ]}

      # Get all contacts from contact_staging for this lead file
      contacts =
        StagedContact
        |> Ash.Query.filter(lead_file_id: lf_id)
        |> Ash.read!()

      rnd_results
      |> Enum.map(fn {phone, _date, result} ->
        case result do
          "yes" ->
            # Find contacts that have this phone number in their metadata
            matching_contacts = find_contacts_with_phone(contacts, phone)

            # Update each matching contact
            matching_contacts
            |> Enum.map(fn contact ->
              update_contact_for_rnd_reassignment(contact, phone)
            end)

          _ ->
            # "no_data", "no" - no action needed either hasn't been reassigned or there's no data and it's assumed
            :no_action
        end
      end)
      |> List.flatten()
      |> Enum.filter(&(&1 != :no_action))
    else
      {:error, error} ->
        IO.inspect(error, label: "error downloading rnd results")
      _ ->
        IO.puts("Other RND Result")
    end
  end

  # Find contacts that have the specified phone number in their phones_metadata
  defp find_contacts_with_phone(contacts, phone) do
    contacts
    |> Enum.filter(fn contact ->
      contact.phones_metadata.all_phones
      |> Enum.any?(fn phone_metadata ->
        phone_metadata.phone_number == phone and phone_metadata.included?
      end)

    end)
  end

  # Update a contact when a phone number has been reassigned (RND "yes" result)
  defp update_contact_for_rnd_reassignment(contact, reassigned_phone) do
    updated_all_phones =
      contact.phones_metadata.all_phones
      |> Enum.map(fn phone_metadata ->
        if phone_metadata.phone_number == reassigned_phone and phone_metadata.included? do
          # Mark this phone as reassigned and excluded
          phone_metadata
          |> Map.put(:rnd?, true)
          |> Map.put(:dnc?, true)
          |> Map.put(:included?, false)
          |> Map.put(:exclusion_reason, "Reassigned Phone")
          |> Map.put(:line_type, "Uncallable")
          |> Map.put(:result_code, "D")
          |> Map.put(:result_code_nn, if(phone_metadata.result_code_nn == nil, do: nil, else: "D"))
        else
          phone_metadata
        end
      end)

    case contact do
      %StagedContact{} = staged_contact ->
        # Create new bulk metadata with updated phone list
        new_bulk = %BulkPhoneMetadata{
          all_phones: updated_all_phones,
          found_phone_count: length(updated_all_phones),
          unique_valid_phone_count: Enum.count(updated_all_phones, & &1.included?)
        }

        # Update individual phone field metadata if the reassigned phone matches specific fields
        phone_field_updates = build_phone_field_updates(staged_contact, reassigned_phone)

        # Combine all updates
        all_updates = Map.put(phone_field_updates, :phones_metadata, new_bulk)

        IO.inspect(all_updates, label: "all rnd updates")

        # Update the contact
        staged_contact
        |> Ash.Changeset.for_update(:update, all_updates)
        |> Ash.update!()

      _ ->
        contact
    end
  end

  # Updates for individual phone fields (homephone_dnc?, homephone_line_type, etc.)
  defp build_phone_field_updates(contact, reassigned_phone) do
    phone_fields = [:homephone, :companyphone, :newphone, :altcompanyphone]

    phone_fields
    |> Enum.reduce(%{}, fn field, updates ->
      phone_value = Map.get(contact, field)

      if phone_value == reassigned_phone do
        dnc_field = String.to_existing_atom("#{field}_dnc?")
        line_type_field = String.to_existing_atom("#{field}_line_type")

        updates
        |> Map.put(dnc_field, true)
        |> Map.put(line_type_field, "Uncallable")
      else
        updates
      end
    end)
  end

end
